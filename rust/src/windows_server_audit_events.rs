// sources:
// https://www.ultimatewindowssecurity.com/securitylog/encyclopedia/default.aspx
// https://learn.microsoft.com/en-us/windows-server/identity/ad-ds/plan/appendix-l--events-to-monitor
// https://ss64.com/ps/syntax-eventids.html
// https://gist.github.com/githubfoam/69eee155e4edafb2e679fb6ac5ea47d0
// https://github.com/PerryvandenHondel/windows-event-id-list-csv/blob/master/windows-event-id.csv

use std::collections::HashMap;
use once_cell::sync::Lazy;

#[derive(Debug)]
pub struct AuditEvent {
    pub category: &'static str,
    pub potential_criticality: &'static str,
    pub summary: &'static str,
}

pub static WINDOWS_SERVER_AUDIT_EVENTS: Lazy<HashMap<&'static str, AuditEvent>> = Lazy::new(|| {
    let mut m = HashMap::new();
    m.insert("00", AuditEvent { category: "Miscellaneous", potential_criticality: "Miscellaneous", summary: "The log was started" });
    m.insert("01", AuditEvent { category: "Miscellaneous", potential_criticality: "Miscellaneous", summary: "The log was stopped" });
    m.insert("02", AuditEvent { category: "Miscellaneous", potential_criticality: "Miscellaneous", summary: "The log was temporarily paused due to low disk space" });
    m.insert("10", AuditEvent { category: "Miscellaneous", potential_criticality: "Miscellaneous", summary: "A new IP address was leased to a client" });
    m.insert("11", AuditEvent { category: "Miscellaneous", potential_criticality: "Miscellaneous", summary: "A lease was renewed by a client" });
    m.insert("12", AuditEvent { category: "Miscellaneous", potential_criticality: "Miscellaneous", summary: "A lease was released by a client" });
    m.insert("13", AuditEvent { category: "Miscellaneous", potential_criticality: "Miscellaneous", summary: "An IP address was found to be in use on the network" });
    m.insert("14", AuditEvent { category: "Miscellaneous", potential_criticality: "Miscellaneous", summary: "A lease request could not be satisfied because the scope's address pool was exhausted" });
    m.insert("15", AuditEvent { category: "Miscellaneous", potential_criticality: "Miscellaneous", summary: "A lease was denied" });
    m.insert("16", AuditEvent { category: "Miscellaneous", potential_criticality: "Miscellaneous", summary: "A lease was deleted" });
    m.insert("17", AuditEvent { category: "Miscellaneous", potential_criticality: "Miscellaneous", summary: "A lease was expired and DNS records for an expired leases have not been deleted" });
    m.insert("18", AuditEvent { category: "Miscellaneous", potential_criticality: "Miscellaneous", summary: "A lease was expired and DNS records were deleted" });
    m.insert("20", AuditEvent { category: "Miscellaneous", potential_criticality: "Miscellaneous", summary: "A BOOTP address was leased to a client" });
    m.insert("21", AuditEvent { category: "Miscellaneous", potential_criticality: "Miscellaneous", summary: "A dynamic BOOTP address was leased to a client" });
    m.insert("22", AuditEvent { category: "Miscellaneous", potential_criticality: "Miscellaneous", summary: "A BOOTP request could not be satisfied because the scope's address pool for BOOTP was exhausted" });
    m.insert("23", AuditEvent { category: "Miscellaneous", potential_criticality: "Miscellaneous", summary: "A BOOTP IP address was deleted after checking to see it was not in use" });
    m.insert("24", AuditEvent { category: "Miscellaneous", potential_criticality: "Miscellaneous", summary: "IP address cleanup operation has began" });
    m.insert("25", AuditEvent { category: "Miscellaneous", potential_criticality: "Miscellaneous", summary: "IP address cleanup statistics" });
    m.insert("29", AuditEvent { category: "Miscellaneous", potential_criticality: "Miscellaneous", summary: "The time provider NtpClient is configured to acquire time from one or more time sources; however none of the sources are currently accessible" });
    m.insert("30", AuditEvent { category: "Miscellaneous", potential_criticality: "Miscellaneous", summary: "DNS update request to the named DNS server" });
    m.insert("31", AuditEvent { category: "Miscellaneous", potential_criticality: "Miscellaneous", summary: "DNS update failed" });
    m.insert("32", AuditEvent { category: "Miscellaneous", potential_criticality: "Miscellaneous", summary: "DNS update successful" });
    m.insert("33", AuditEvent { category: "Miscellaneous", potential_criticality: "Miscellaneous", summary: "Packet dropped due to NAP policy" });
    m.insert("34", AuditEvent { category: "Miscellaneous", potential_criticality: "Miscellaneous", summary: "DNS update request failed.as the DNS update request queue limit exceeded" });
    m.insert("35", AuditEvent { category: "Miscellaneous", potential_criticality: "Miscellaneous", summary: "DNS update request failed" });
    m.insert("36", AuditEvent { category: "Miscellaneous", potential_criticality: "Miscellaneous", summary: "Packet dropped because the server is in failover standby role or the hash of the client ID does not match" });
    m.insert("38", AuditEvent { category: "Miscellaneous", potential_criticality: "Miscellaneous", summary: "The time provider NtpClient cannot reach or is currently receiving invalid time data" });
    m.insert("41", AuditEvent { category: "Miscellaneous", potential_criticality: "Miscellaneous", summary: "The system has rebooted without cleanly shutting down first" });
    m.insert("47", AuditEvent { category: "Miscellaneous", potential_criticality: "Miscellaneous", summary: "Time Provider NtpClient: No valid response received" });
    m.insert("50", AuditEvent { category: "Miscellaneous", potential_criticality: "Miscellaneous", summary: "Unreachable domain" });
    m.insert("51", AuditEvent { category: "Miscellaneous", potential_criticality: "Miscellaneous", summary: "Authorization succeeded" });
    m.insert("52", AuditEvent { category: "Miscellaneous", potential_criticality: "Miscellaneous", summary: "Upgraded to a Windows Server 2003 operating system" });
    m.insert("53", AuditEvent { category: "Miscellaneous", potential_criticality: "Miscellaneous", summary: "Cached Authorization" });
    m.insert("54", AuditEvent { category: "Miscellaneous", potential_criticality: "Miscellaneous", summary: "Authorization failed" });
    m.insert("55", AuditEvent { category: "Miscellaneous", potential_criticality: "Miscellaneous", summary: "Authorization (servicing)" });
    m.insert("56", AuditEvent { category: "Miscellaneous", potential_criticality: "Miscellaneous", summary: "Authorization failure, stopped servicing" });
    m.insert("57", AuditEvent { category: "Miscellaneous", potential_criticality: "Miscellaneous", summary: "Server found in domain" });
    m.insert("58", AuditEvent { category: "Miscellaneous", potential_criticality: "Miscellaneous", summary: "Server could not find domain" });
    m.insert("59", AuditEvent { category: "Miscellaneous", potential_criticality: "Miscellaneous", summary: "Network failure" });
    m.insert("60", AuditEvent { category: "Miscellaneous", potential_criticality: "Miscellaneous", summary: "No DC is DS Enabled" });
    m.insert("61", AuditEvent { category: "Miscellaneous", potential_criticality: "Miscellaneous", summary: "Server found that belongs to DS domain" });
    m.insert("62", AuditEvent { category: "Miscellaneous", potential_criticality: "Miscellaneous", summary: "Another server found" });
    m.insert("63", AuditEvent { category: "Miscellaneous", potential_criticality: "Miscellaneous", summary: "Restarting rogue detection" });
    m.insert("64", AuditEvent { category: "Miscellaneous", potential_criticality: "Miscellaneous", summary: "No DHCP enabled interfaces" });
    m.insert("512", AuditEvent { category: "Miscellaneous", potential_criticality: "Miscellaneous", summary: "Windows NT is starting up" });
    m.insert("513", AuditEvent { category: "Miscellaneous", potential_criticality: "Miscellaneous", summary: "Windows is shutting down" });
    m.insert("514", AuditEvent { category: "Miscellaneous", potential_criticality: "Miscellaneous", summary: "An authentication package has been loaded by the Local Security Authority" });
    m.insert("515", AuditEvent { category: "Miscellaneous", potential_criticality: "Miscellaneous", summary: "A trusted logon process has registered with the Local Security Authority" });
    m.insert("516", AuditEvent { category: "Miscellaneous", potential_criticality: "Miscellaneous", summary: "Internal resources allocated for the queuing of audit messages have been exhausted, leading to the loss of some audits" });
    m.insert("517", AuditEvent { category: "Miscellaneous", potential_criticality: "Miscellaneous", summary: "The audit log was cleared" });
    m.insert("518", AuditEvent { category: "Miscellaneous", potential_criticality: "Miscellaneous", summary: "A notification package has been loaded by the Security Account Manager" });
    m.insert("519", AuditEvent { category: "Miscellaneous", potential_criticality: "Miscellaneous", summary: "A process is using an invalid local procedure call (LPC) port" });
    m.insert("520", AuditEvent { category: "Miscellaneous", potential_criticality: "Miscellaneous", summary: "The system time was changed" });
    m.insert("521", AuditEvent { category: "Miscellaneous", potential_criticality: "Miscellaneous", summary: "Unable to log events to security log" });
    m.insert("528", AuditEvent { category: "Miscellaneous", potential_criticality: "Miscellaneous", summary: "Successful Logon" });
    m.insert("529", AuditEvent { category: "Miscellaneous", potential_criticality: "Miscellaneous", summary: "Logon Failure - Unknown user name or bad password" });
    m.insert("530", AuditEvent { category: "Miscellaneous", potential_criticality: "Miscellaneous", summary: "Logon Failure - Account logon time restriction violation" });
    m.insert("531", AuditEvent { category: "Miscellaneous", potential_criticality: "Miscellaneous", summary: "Logon Failure - Account currently disabled" });
    m.insert("532", AuditEvent { category: "Miscellaneous", potential_criticality: "Miscellaneous", summary: "Logon Failure - The specified user account has expired" });
    m.insert("533", AuditEvent { category: "Miscellaneous", potential_criticality: "Miscellaneous", summary: "Logon Failure - User not allowed to logon at this computer" });
    m.insert("534", AuditEvent { category: "Miscellaneous", potential_criticality: "Miscellaneous", summary: "Logon Failure - The user has not been granted the requested logon type at this machine" });
    m.insert("535", AuditEvent { category: "Miscellaneous", potential_criticality: "Miscellaneous", summary: "Logon Failure - The specified account's password has expired" });
    m.insert("536", AuditEvent { category: "Miscellaneous", potential_criticality: "Miscellaneous", summary: "Logon Failure - The NetLogon component is not active" });
    m.insert("537", AuditEvent { category: "Miscellaneous", potential_criticality: "Miscellaneous", summary: "Logon failure - The logon attempt failed for other reasons" });
    m.insert("538", AuditEvent { category: "Miscellaneous", potential_criticality: "Miscellaneous", summary: "User Logoff" });
    m.insert("539", AuditEvent { category: "Miscellaneous", potential_criticality: "Miscellaneous", summary: "Logon Failure - Account locked out" });
    m.insert("540", AuditEvent { category: "Miscellaneous", potential_criticality: "Miscellaneous", summary: "Successful Network Logon" });
    m.insert("551", AuditEvent { category: "Miscellaneous", potential_criticality: "Miscellaneous", summary: "User initiated logoff" });
    m.insert("552", AuditEvent { category: "Miscellaneous", potential_criticality: "Miscellaneous", summary: "Logon attempt using explicit credentials" });
    m.insert("560", AuditEvent { category: "Miscellaneous", potential_criticality: "Miscellaneous", summary: "Object Open" });
    m.insert("561", AuditEvent { category: "Miscellaneous", potential_criticality: "Miscellaneous", summary: "Handle Allocated" });
    m.insert("562", AuditEvent { category: "Miscellaneous", potential_criticality: "Miscellaneous", summary: "Handle Closed" });
    m.insert("563", AuditEvent { category: "Miscellaneous", potential_criticality: "Miscellaneous", summary: "Object Open for Delete" });
    m.insert("564", AuditEvent { category: "Miscellaneous", potential_criticality: "Miscellaneous", summary: "Object Deleted" });
    m.insert("565", AuditEvent { category: "Miscellaneous", potential_criticality: "Miscellaneous", summary: "Object Open (Active Directory)" });
    m.insert("566", AuditEvent { category: "Miscellaneous", potential_criticality: "Miscellaneous", summary: "Object Operation (W3 Active Directory)" });
    m.insert("567", AuditEvent { category: "Miscellaneous", potential_criticality: "Miscellaneous", summary: "Object Access Attempt" });
    m.insert("576", AuditEvent { category: "Miscellaneous", potential_criticality: "Miscellaneous", summary: "Special privileges assigned to new logon" });
    m.insert("577", AuditEvent { category: "Miscellaneous", potential_criticality: "Miscellaneous", summary: "Privileged Service Called" });
    m.insert("578", AuditEvent { category: "Miscellaneous", potential_criticality: "Miscellaneous", summary: "Privileged object operation" });
    m.insert("592", AuditEvent { category: "Miscellaneous", potential_criticality: "Miscellaneous", summary: "A new process has been created" });
    m.insert("593", AuditEvent { category: "Miscellaneous", potential_criticality: "Miscellaneous", summary: "A process has exited" });
    m.insert("594", AuditEvent { category: "Miscellaneous", potential_criticality: "Miscellaneous", summary: "A handle to an object has been duplicated" });
    m.insert("595", AuditEvent { category: "Miscellaneous", potential_criticality: "Miscellaneous", summary: "Indirect access to an object has been obtained" });
    m.insert("596", AuditEvent { category: "Miscellaneous", potential_criticality: "Miscellaneous", summary: "Backup of data protection master key" });
    m.insert("600", AuditEvent { category: "Miscellaneous", potential_criticality: "Miscellaneous", summary: "A process was assigned a primary token" });
    m.insert("601", AuditEvent { category: "Miscellaneous", potential_criticality: "Miscellaneous", summary: "Attempt to install service" });
    m.insert("602", AuditEvent { category: "Miscellaneous", potential_criticality: "Miscellaneous", summary: "Scheduled Task created" });
    m.insert("608", AuditEvent { category: "Miscellaneous", potential_criticality: "Miscellaneous", summary: "User Right Assigned" });
    m.insert("609", AuditEvent { category: "Miscellaneous", potential_criticality: "Miscellaneous", summary: "User Right Removed" });
    m.insert("610", AuditEvent { category: "Miscellaneous", potential_criticality: "Miscellaneous", summary: "New Trusted Domain" });
    m.insert("611", AuditEvent { category: "Miscellaneous", potential_criticality: "Miscellaneous", summary: "Removing Trusted Domain" });
    m.insert("612", AuditEvent { category: "Miscellaneous", potential_criticality: "Miscellaneous", summary: "Audit Policy Change" });
    m.insert("613", AuditEvent { category: "Miscellaneous", potential_criticality: "Miscellaneous", summary: "IPSec policy agent started" });
    m.insert("614", AuditEvent { category: "Miscellaneous", potential_criticality: "Miscellaneous", summary: "IPSec policy agent disabled" });
    m.insert("615", AuditEvent { category: "Miscellaneous", potential_criticality: "Miscellaneous", summary: "IPSEC PolicyAgent Service" });
    m.insert("616", AuditEvent { category: "Miscellaneous", potential_criticality: "Miscellaneous", summary: "IPSec policy agent encountered a potentially serious failure" });
    m.insert("617", AuditEvent { category: "Miscellaneous", potential_criticality: "Miscellaneous", summary: "Kerberos Policy Changed" });
    m.insert("618", AuditEvent { category: "Miscellaneous", potential_criticality: "Miscellaneous", summary: "Encrypted Data Recovery Policy Changed" });
    m.insert("619", AuditEvent { category: "Miscellaneous", potential_criticality: "Miscellaneous", summary: "Quality of Service Policy Changed" });
    m.insert("620", AuditEvent { category: "Miscellaneous", potential_criticality: "Miscellaneous", summary: "Trusted Domain Information Modified" });
    m.insert("621", AuditEvent { category: "Miscellaneous", potential_criticality: "Miscellaneous", summary: "System Security Access Granted" });
    m.insert("622", AuditEvent { category: "Miscellaneous", potential_criticality: "Miscellaneous", summary: "System Security Access Removed" });
    m.insert("623", AuditEvent { category: "Miscellaneous", potential_criticality: "Miscellaneous", summary: "Per User Audit Policy was refreshed" });
    m.insert("624", AuditEvent { category: "Miscellaneous", potential_criticality: "Miscellaneous", summary: "User Account Created" });
    m.insert("625", AuditEvent { category: "Miscellaneous", potential_criticality: "Miscellaneous", summary: "User Account Type Changed" });
    m.insert("626", AuditEvent { category: "Miscellaneous", potential_criticality: "Miscellaneous", summary: "User Account Enabled" });
    m.insert("627", AuditEvent { category: "Miscellaneous", potential_criticality: "Miscellaneous", summary: "Change Password Attempt" });
    m.insert("628", AuditEvent { category: "Miscellaneous", potential_criticality: "Miscellaneous", summary: "User Account password set" });
    m.insert("629", AuditEvent { category: "Miscellaneous", potential_criticality: "Miscellaneous", summary: "User Account Disabled" });
    m.insert("630", AuditEvent { category: "Miscellaneous", potential_criticality: "Miscellaneous", summary: "User Account Deleted" });
    m.insert("631", AuditEvent { category: "Miscellaneous", potential_criticality: "Miscellaneous", summary: "Security Enabled Global Group Created" });
    m.insert("632", AuditEvent { category: "Miscellaneous", potential_criticality: "Miscellaneous", summary: "Security Enabled Global Group Member Added" });
    m.insert("633", AuditEvent { category: "Miscellaneous", potential_criticality: "Miscellaneous", summary: "Security Enabled Global Group Member Removed" });
    m.insert("634", AuditEvent { category: "Miscellaneous", potential_criticality: "Miscellaneous", summary: "Security Enabled Global Group Deleted" });
    m.insert("635", AuditEvent { category: "Miscellaneous", potential_criticality: "Miscellaneous", summary: "Security Enabled Local Group Created" });
    m.insert("636", AuditEvent { category: "Miscellaneous", potential_criticality: "Miscellaneous", summary: "Security Enabled Local Group Member Added" });
    m.insert("637", AuditEvent { category: "Miscellaneous", potential_criticality: "Miscellaneous", summary: "Security Enabled Local Group Member Removed" });
    m.insert("638", AuditEvent { category: "Miscellaneous", potential_criticality: "Miscellaneous", summary: "Security Enabled Local Group Deleted" });
    m.insert("639", AuditEvent { category: "Miscellaneous", potential_criticality: "Miscellaneous", summary: "Security Enabled Local Group Changed" });
    m.insert("640", AuditEvent { category: "Miscellaneous", potential_criticality: "Miscellaneous", summary: "General Account Database Change" });
    m.insert("641", AuditEvent { category: "Miscellaneous", potential_criticality: "Miscellaneous", summary: "Security Enabled Global Group Changed" });
    m.insert("642", AuditEvent { category: "Miscellaneous", potential_criticality: "Miscellaneous", summary: "User Account Changed" });
    m.insert("643", AuditEvent { category: "Miscellaneous", potential_criticality: "Miscellaneous", summary: "Domain Policy Changed" });
    m.insert("644", AuditEvent { category: "Miscellaneous", potential_criticality: "Miscellaneous", summary: "User Account Locked Out" });
    m.insert("645", AuditEvent { category: "Miscellaneous", potential_criticality: "Miscellaneous", summary: "Computer Account Created" });
    m.insert("646", AuditEvent { category: "Miscellaneous", potential_criticality: "Miscellaneous", summary: "Computer Account Changed" });
    m.insert("647", AuditEvent { category: "Miscellaneous", potential_criticality: "Miscellaneous", summary: "Computer Account Deleted" });
    m.insert("648", AuditEvent { category: "Miscellaneous", potential_criticality: "Miscellaneous", summary: "Security Disabled Local Group Created" });
    m.insert("649", AuditEvent { category: "Miscellaneous", potential_criticality: "Miscellaneous", summary: "Security Disabled Local Group Changed" });
    m.insert("650", AuditEvent { category: "Miscellaneous", potential_criticality: "Miscellaneous", summary: "Security Disabled Local Group Member Added" });
    m.insert("651", AuditEvent { category: "Miscellaneous", potential_criticality: "Miscellaneous", summary: "Security Disabled Local Group Member Removed" });
    m.insert("652", AuditEvent { category: "Miscellaneous", potential_criticality: "Miscellaneous", summary: "Security Disabled Local Group Deleted" });
    m.insert("653", AuditEvent { category: "Miscellaneous", potential_criticality: "Miscellaneous", summary: "Security Disabled Global Group Created" });
    m.insert("654", AuditEvent { category: "Miscellaneous", potential_criticality: "Miscellaneous", summary: "Security Disabled Global Group Changed" });
    m.insert("655", AuditEvent { category: "Miscellaneous", potential_criticality: "Miscellaneous", summary: "Security Disabled Global Group Member Added" });
    m.insert("656", AuditEvent { category: "Miscellaneous", potential_criticality: "Miscellaneous", summary: "Security Disabled Global Group Member Removed" });
    m.insert("657", AuditEvent { category: "Miscellaneous", potential_criticality: "Miscellaneous", summary: "Security Disabled Global Group Deleted" });
    m.insert("658", AuditEvent { category: "Miscellaneous", potential_criticality: "Miscellaneous", summary: "Security Enabled Universal Group Created" });
    m.insert("659", AuditEvent { category: "Miscellaneous", potential_criticality: "Miscellaneous", summary: "Security Enabled Universal Group Changed" });
    m.insert("660", AuditEvent { category: "Miscellaneous", potential_criticality: "Miscellaneous", summary: "Security Enabled Universal Group Member Added" });
    m.insert("661", AuditEvent { category: "Miscellaneous", potential_criticality: "Miscellaneous", summary: "Security Enabled Universal Group Member Removed" });
    m.insert("662", AuditEvent { category: "Miscellaneous", potential_criticality: "Miscellaneous", summary: "Security Enabled Universal Group Deleted" });
    m.insert("663", AuditEvent { category: "Miscellaneous", potential_criticality: "Miscellaneous", summary: "Security Disabled Universal Group Created" });
    m.insert("664", AuditEvent { category: "Miscellaneous", potential_criticality: "Miscellaneous", summary: "Security Disabled Universal Group Changed" });
    m.insert("665", AuditEvent { category: "Miscellaneous", potential_criticality: "Miscellaneous", summary: "Security Disabled Universal Group Member Added" });
    m.insert("666", AuditEvent { category: "Miscellaneous", potential_criticality: "Miscellaneous", summary: "Security Disabled Universal Group Member Removed" });
    m.insert("667", AuditEvent { category: "Miscellaneous", potential_criticality: "Miscellaneous", summary: "Security Disabled Universal Group Deleted" });
    m.insert("668", AuditEvent { category: "Miscellaneous", potential_criticality: "Miscellaneous", summary: "Group Type Changed" });
    m.insert("669", AuditEvent { category: "Miscellaneous", potential_criticality: "Miscellaneous", summary: "Add SID History" });
    m.insert("670", AuditEvent { category: "Miscellaneous", potential_criticality: "Miscellaneous", summary: "Add SID History" });
    m.insert("671", AuditEvent { category: "Miscellaneous", potential_criticality: "Miscellaneous", summary: "User Account Unlocked" });
    m.insert("672", AuditEvent { category: "Miscellaneous", potential_criticality: "Miscellaneous", summary: "Authentication Ticket Granted" });
    m.insert("673", AuditEvent { category: "Miscellaneous", potential_criticality: "Miscellaneous", summary: "Service Ticket Granted" });
    m.insert("674", AuditEvent { category: "Miscellaneous", potential_criticality: "Miscellaneous", summary: "Ticket Granted Renewed" });
    m.insert("675", AuditEvent { category: "Miscellaneous", potential_criticality: "Miscellaneous", summary: "Pre-authentication failed" });
    m.insert("676", AuditEvent { category: "Miscellaneous", potential_criticality: "Miscellaneous", summary: "Authentication Ticket Request Failed" });
    m.insert("677", AuditEvent { category: "Miscellaneous", potential_criticality: "Miscellaneous", summary: "Service Ticket Request Failed" });
    m.insert("678", AuditEvent { category: "Miscellaneous", potential_criticality: "Miscellaneous", summary: "Account Mapped for Logon by" });
    m.insert("679", AuditEvent { category: "Miscellaneous", potential_criticality: "Miscellaneous", summary: "The name: %2 could not be mapped for logon by: %1" });
    m.insert("680", AuditEvent { category: "Miscellaneous", potential_criticality: "Miscellaneous", summary: "Account Used for Logon by" });
    m.insert("681", AuditEvent { category: "Miscellaneous", potential_criticality: "Miscellaneous", summary: "The logon to account: %2 by: %1 from workstation: %3 failed" });
    m.insert("682", AuditEvent { category: "Miscellaneous", potential_criticality: "Miscellaneous", summary: "Session reconnected to winstation" });
    m.insert("683", AuditEvent { category: "Miscellaneous", potential_criticality: "Miscellaneous", summary: "Session disconnected from winstation" });
    m.insert("684", AuditEvent { category: "Miscellaneous", potential_criticality: "Miscellaneous", summary: "Set ACLs of members in administrators groups" });
    m.insert("685", AuditEvent { category: "Miscellaneous", potential_criticality: "Miscellaneous", summary: "Account Name Changed" });
    m.insert("686", AuditEvent { category: "Miscellaneous", potential_criticality: "Miscellaneous", summary: "Password of the following user accessed" });
    m.insert("687", AuditEvent { category: "Miscellaneous", potential_criticality: "Miscellaneous", summary: "Basic Application Group Created" });
    m.insert("688", AuditEvent { category: "Miscellaneous", potential_criticality: "Miscellaneous", summary: "Basic Application Group Changed" });
    m.insert("689", AuditEvent { category: "Miscellaneous", potential_criticality: "Miscellaneous", summary: "Basic Application Group Member Added" });
    m.insert("690", AuditEvent { category: "Miscellaneous", potential_criticality: "Miscellaneous", summary: "Basic Application Group Member Removed" });
    m.insert("691", AuditEvent { category: "Miscellaneous", potential_criticality: "Miscellaneous", summary: "Basic Application Group Non-Member Added" });
    m.insert("692", AuditEvent { category: "Miscellaneous", potential_criticality: "Miscellaneous", summary: "Basic Application Group Non-Member Removed" });
    m.insert("693", AuditEvent { category: "Miscellaneous", potential_criticality: "Miscellaneous", summary: "Basic Application Group Deleted" });
    m.insert("694", AuditEvent { category: "Miscellaneous", potential_criticality: "Miscellaneous", summary: "LDAP Query Group Created" });
    m.insert("695", AuditEvent { category: "Miscellaneous", potential_criticality: "Miscellaneous", summary: "LDAP Query Group Changed" });
    m.insert("696", AuditEvent { category: "Miscellaneous", potential_criticality: "Miscellaneous", summary: "LDAP Query Group Deleted" });
    m.insert("697", AuditEvent { category: "Miscellaneous", potential_criticality: "Miscellaneous", summary: "Password Policy Checking API is called" });
    m.insert("806", AuditEvent { category: "Miscellaneous", potential_criticality: "Miscellaneous", summary: "Per User Audit Policy was refreshed" });
    m.insert("807", AuditEvent { category: "Miscellaneous", potential_criticality: "Miscellaneous", summary: "Per user auditing policy set for user" });
    m.insert("808", AuditEvent { category: "Miscellaneous", potential_criticality: "Miscellaneous", summary: "A security event source has attempted to register" });
    m.insert("809", AuditEvent { category: "Miscellaneous", potential_criticality: "Miscellaneous", summary: "A security event source has attempted to unregister" });
    m.insert("848", AuditEvent { category: "Miscellaneous", potential_criticality: "Miscellaneous", summary: "The following policy was active when the Windows Firewall started" });
    m.insert("849", AuditEvent { category: "Miscellaneous", potential_criticality: "Miscellaneous", summary: "An application was listed as an exception when the Windows Firewall started" });
    m.insert("850", AuditEvent { category: "Miscellaneous", potential_criticality: "Miscellaneous", summary: "A port was listed as an exception when the Windows Firewall started" });
    m.insert("851", AuditEvent { category: "Miscellaneous", potential_criticality: "Miscellaneous", summary: "A change has been made to the Windows Firewall application exception list" });
    m.insert("852", AuditEvent { category: "Miscellaneous", potential_criticality: "Miscellaneous", summary: "A change has been made to the Windows Firewall port exception list" });
    m.insert("853", AuditEvent { category: "Miscellaneous", potential_criticality: "Miscellaneous", summary: "The Windows Firewall operational mode has changed" });
    m.insert("854", AuditEvent { category: "Miscellaneous", potential_criticality: "Miscellaneous", summary: "The Windows Firewall logging settings have changed" });
    m.insert("855", AuditEvent { category: "Miscellaneous", potential_criticality: "Miscellaneous", summary: "A Windows Firewall ICMP setting has changed" });
    m.insert("856", AuditEvent { category: "Miscellaneous", potential_criticality: "Miscellaneous", summary: "The Windows Firewall setting to allow unicast responses to multicast/broadcast traffic has changed" });
    m.insert("857", AuditEvent { category: "Miscellaneous", potential_criticality: "Miscellaneous", summary: "The Windows Firewall setting to allow remote administration, allowing port TCP 135 and DCOM/RPC, has changed" });
    m.insert("858", AuditEvent { category: "Miscellaneous", potential_criticality: "Miscellaneous", summary: "Windows Firewall group policy settings have been applied" });
    m.insert("859", AuditEvent { category: "Miscellaneous", potential_criticality: "Miscellaneous", summary: "The Windows Firewall group policy settings have been removed" });
    m.insert("860", AuditEvent { category: "Miscellaneous", potential_criticality: "Miscellaneous", summary: "The Windows Firewall has switched the active policy profile" });
    m.insert("861", AuditEvent { category: "Miscellaneous", potential_criticality: "Miscellaneous", summary: "The Windows Firewall has detected an application listening for incoming traffic" });
    m.insert("1030", AuditEvent { category: "Miscellaneous", potential_criticality: "Miscellaneous", summary: "the Group Policy settings cannot be read, the Group Policy object (GPO) is corrupted, or the computer is unable to access the domain controller" });
    m.insert("1058", AuditEvent { category: "Miscellaneous", potential_criticality: "Miscellaneous", summary: "the computer is unable to access the Sysvol share, which stores the Group Policy templates and scripts" });
    m.insert("1074", AuditEvent { category: "Miscellaneous", potential_criticality: "Miscellaneous", summary: "The system has been shutdown properly by a user or process. The process X has initiated the restart / shutdown of computer on behalf of user Y for the following reason: Z. Indicates that an application or a user initiated a restart or shutdown" });
    m.insert("1076", AuditEvent { category: "Miscellaneous", potential_criticality: "Miscellaneous", summary: "The reason supplied by user X for the last unexpected shutdown of this computer is: Y. Records when the first user with shutdown privileges logs on to the computer after an unexpected restart or shutdown and supplies a reason for the occurrence.Follows after Event ID 6008 and means that the first user with shutdown privileges logged on to the server after an unexpected restart or shutdown and specified the cause" });
    m.insert("1100", AuditEvent { category: "Miscellaneous", potential_criticality: "Miscellaneous", summary: "The event logging service has shut down" });
    m.insert("1101", AuditEvent { category: "Miscellaneous", potential_criticality: "Miscellaneous", summary: "Audit events have been dropped by the transport" });
    m.insert("1102", AuditEvent { category: "Miscellaneous", potential_criticality: "Medium to High", summary: "The audit log was cleared" });
    m.insert("1104", AuditEvent { category: "Miscellaneous", potential_criticality: "Miscellaneous", summary: "The security Log is now full" });
    m.insert("1105", AuditEvent { category: "Miscellaneous", potential_criticality: "Miscellaneous", summary: "Event log automatic backup" });
    m.insert("1108", AuditEvent { category: "Miscellaneous", potential_criticality: "Miscellaneous", summary: "The event logging service encountered an error" });
    m.insert("1704", AuditEvent { category: "Miscellaneous", potential_criticality: "Miscellaneous", summary: "the GPO was successfully applied to the client computer" });
    m.insert("4190", AuditEvent { category: "Miscellaneous", potential_criticality: "Miscellaneous", summary: "DHCP server failed to assign an address because there are no more available in the scope" });
    m.insert("4191", AuditEvent { category: "Miscellaneous", potential_criticality: "Miscellaneous", summary: "DHCP server encountered an error while processing a DHCP request" });
    m.insert("4198", AuditEvent { category: "Miscellaneous", potential_criticality: "Miscellaneous", summary: "DHCP lease has expired" });
    m.insert("4608", AuditEvent { category: "System", potential_criticality: "Low", summary: "Windows is starting up" });
    m.insert("4609", AuditEvent { category: "System", potential_criticality: "Low", summary: "Windows is shutting down" });
    m.insert("4610", AuditEvent { category: "System", potential_criticality: "Low", summary: "An authentication package has been loaded by the Local Security Authority" });
    m.insert("4611", AuditEvent { category: "System", potential_criticality: "Low", summary: "A trusted logon process has been registered with the Local Security Authority" });
    m.insert("4612", AuditEvent { category: "System", potential_criticality: "Low", summary: "Internal resources allocated for the queuing of audit messages have been exhausted, leading to the loss of some audits" });
    m.insert("4614", AuditEvent { category: "System", potential_criticality: "Low", summary: "A notification package has been loaded by the Security Account Manager" });
    m.insert("4615", AuditEvent { category: "System", potential_criticality: "Low", summary: "Invalid use of LPC port" });
    m.insert("4616", AuditEvent { category: "System", potential_criticality: "Low", summary: "The system time was changed" });
    m.insert("4618", AuditEvent { category: "System", potential_criticality: "High", summary: "A monitored security event pattern has occurred" });
    m.insert("4621", AuditEvent { category: "System", potential_criticality: "Medium", summary: "Administrator recovered system from CrashOnAuditFail. Users who are not administrators will now be allowed to log on. Some auditable activity might not have been recorded" });
    m.insert("4622", AuditEvent { category: "System", potential_criticality: "Low", summary: "A security package has been loaded by the Local Security Authority" });
    m.insert("4624", AuditEvent { category: "Logon/Logoff", potential_criticality: "Low", summary: "An account was successfully logged on" });
    m.insert("4625", AuditEvent { category: "Logon/Logoff", potential_criticality: "Low", summary: "An account failed to log on" });
    m.insert("4626", AuditEvent { category: "Logon/Logoff", potential_criticality: "Low", summary: "User/Device claims information" });
    m.insert("4627", AuditEvent { category: "Logon/Logoff", potential_criticality: "Low", summary: "Group membership information" });
    m.insert("4634", AuditEvent { category: "Logon/Logoff", potential_criticality: "Low", summary: "An account was logged off" });
    m.insert("4646", AuditEvent { category: "Logon/Logoff", potential_criticality: "Low", summary: "IKE DoS-prevention mode started" });
    m.insert("4647", AuditEvent { category: "Logon/Logoff", potential_criticality: "Low", summary: "User initiated logoff" });
    m.insert("4648", AuditEvent { category: "Logon/Logoff", potential_criticality: "Low", summary: "A logon was attempted using explicit credentials" });
    m.insert("4649", AuditEvent { category: "Logon/Logoff", potential_criticality: "High", summary: "A replay attack was detected. May be a harmless false positive due to misconfiguration error" });
    m.insert("4650", AuditEvent { category: "Logon/Logoff", potential_criticality: "Low", summary: "An IPsec Main Mode security association was established. Extended Mode was not enabled. Certificate authentication was not used" });
    m.insert("4651", AuditEvent { category: "Logon/Logoff", potential_criticality: "Low", summary: "An IPsec Main Mode security association was established. Extended Mode was not enabled. A certificate was used for authentication" });
    m.insert("4652", AuditEvent { category: "Logon/Logoff", potential_criticality: "Low", summary: "An IPsec Main Mode negotiation failed" });
    m.insert("4653", AuditEvent { category: "Logon/Logoff", potential_criticality: "Low", summary: "An IPsec Main Mode negotiation failed" });
    m.insert("4654", AuditEvent { category: "Logon/Logoff", potential_criticality: "Low", summary: "An IPsec Quick Mode negotiation failed" });
    m.insert("4655", AuditEvent { category: "Logon/Logoff", potential_criticality: "Low", summary: "An IPsec Main Mode security association ended" });
    m.insert("4656", AuditEvent { category: "Object Access", potential_criticality: "Low", summary: "A handle to an object was requested" });
    m.insert("4657", AuditEvent { category: "Object Access", potential_criticality: "Low", summary: "A registry value was modified" });
    m.insert("4658", AuditEvent { category: "Object Access", potential_criticality: "Low", summary: "The handle to an object was closed" });
    m.insert("4659", AuditEvent { category: "Object Access", potential_criticality: "Low", summary: "A handle to an object was requested with intent to delete" });
    m.insert("4660", AuditEvent { category: "Object Access", potential_criticality: "Low", summary: "An object was deleted" });
    m.insert("4661", AuditEvent { category: "Object Access", potential_criticality: "Low", summary: "A handle to an object was requested" });
    m.insert("4662", AuditEvent { category: "DS Access", potential_criticality: "Low", summary: "An operation was performed on an object" });
    m.insert("4663", AuditEvent { category: "Object Access", potential_criticality: "Low", summary: "An attempt was made to access an object" });
    m.insert("4664", AuditEvent { category: "Object Access", potential_criticality: "Low", summary: "An attempt was made to create a hard link" });
    m.insert("4665", AuditEvent { category: "Object Access", potential_criticality: "Low", summary: "An attempt was made to create an application client context" });
    m.insert("4666", AuditEvent { category: "Object Access", potential_criticality: "Low", summary: "An application attempted an operation" });
    m.insert("4667", AuditEvent { category: "Object Access", potential_criticality: "Low", summary: "An application client context was deleted" });
    m.insert("4668", AuditEvent { category: "Object Access", potential_criticality: "Low", summary: "An application was initialized" });
    m.insert("4670", AuditEvent { category: "Policy Change", potential_criticality: "Low", summary: "Permissions on an object were changed" });
    m.insert("4671", AuditEvent { category: "Object Access", potential_criticality: "Low", summary: "An application attempted to access a blocked ordinal through the TBS" });
    m.insert("4672", AuditEvent { category: "Privilege Use", potential_criticality: "Low", summary: "Special privileges assigned to new logon" });
    m.insert("4673", AuditEvent { category: "Privilege Use", potential_criticality: "Low", summary: "A privileged service was called" });
    m.insert("4674", AuditEvent { category: "Privilege Use", potential_criticality: "Low", summary: "An operation was attempted on a privileged object" });
    m.insert("4675", AuditEvent { category: "Logon/Logoff", potential_criticality: "Medium", summary: "SIDs were filtered" });
    m.insert("4688", AuditEvent { category: "Detailed Tracking", potential_criticality: "Low", summary: "A new process has been created" });
    m.insert("4689", AuditEvent { category: "Detailed Tracking", potential_criticality: "Low", summary: "A process has exited" });
    m.insert("4690", AuditEvent { category: "Object Access", potential_criticality: "Low", summary: "An attempt was made to duplicate a handle to an object" });
    m.insert("4691", AuditEvent { category: "Object Access", potential_criticality: "Low", summary: "Indirect access to an object was requested" });
    m.insert("4692", AuditEvent { category: "Detailed Tracking", potential_criticality: "Medium", summary: "Backup of data protection master key was attempted" });
    m.insert("4693", AuditEvent { category: "Detailed Tracking", potential_criticality: "Medium", summary: "Recovery of data protection master key was attempted" });
    m.insert("4694", AuditEvent { category: "Detailed Tracking", potential_criticality: "Low", summary: "Protection of auditable protected data was attempted" });
    m.insert("4695", AuditEvent { category: "Detailed Tracking", potential_criticality: "Low", summary: "Unprotection of auditable protected data was attempted" });
    m.insert("4696", AuditEvent { category: "Detailed Tracking", potential_criticality: "Low", summary: "A primary token was assigned to process" });
    m.insert("4697", AuditEvent { category: "System", potential_criticality: "Low", summary: "A service was installed in the system" });
    m.insert("4698", AuditEvent { category: "Object Access", potential_criticality: "Low", summary: "A scheduled task was created" });
    m.insert("4699", AuditEvent { category: "Object Access", potential_criticality: "Low", summary: "A scheduled task was deleted" });
    m.insert("4700", AuditEvent { category: "Object Access", potential_criticality: "Low", summary: "A scheduled task was enabled" });
    m.insert("4701", AuditEvent { category: "Object Access", potential_criticality: "Low", summary: "A scheduled task was disabled" });
    m.insert("4702", AuditEvent { category: "Object Access", potential_criticality: "Low", summary: "A scheduled task was updated" });
    m.insert("4703", AuditEvent { category: "Policy Change", potential_criticality: "Low", summary: "A user right was adjusted" });
    m.insert("4704", AuditEvent { category: "Policy Change", potential_criticality: "Low", summary: "A user right was assigned" });
    m.insert("4705", AuditEvent { category: "Policy Change", potential_criticality: "Low", summary: "A user right was removed" });
    m.insert("4706", AuditEvent { category: "Policy Change", potential_criticality: "Medium", summary: "A new trust was created to a domain" });
    m.insert("4707", AuditEvent { category: "Policy Change", potential_criticality: "Low", summary: "A trust to a domain was removed" });
    m.insert("4709", AuditEvent { category: "Policy Change", potential_criticality: "Low", summary: "IPsec Services was started" });
    m.insert("4710", AuditEvent { category: "Policy Change", potential_criticality: "Low", summary: "IPsec Services was disabled" });
    m.insert("4711", AuditEvent { category: "Policy Change", potential_criticality: "Low", summary: "May contain any one of the following: PAStore Engine applied locally cached copy of Active Directory storage IPsec policy on the computer. PAStore Engine applied Active Directory storage IPsec policy on the computer. PAStore Engine applied local registry storage IPsec policy on the computer. PAStore Engine failed to apply locally cached copy of Active Directory storage IPsec policy on the computer. PAStore Engine failed to apply Active Directory storage IPsec policy on the computer. PAStore Engine failed to apply local registry storage IPsec policy on the computer. PAStore Engine failed to apply some rules of the active IPsec policy on the computer. PAStore Engine failed to load directory storage IPsec policy on the computer. PAStore Engine loaded directory storage IPsec policy on the computer. PAStore Engine failed to load local storage IPsec policy on the computer. PAStore Engine loaded local storage IPsec policy on the computer. PAStore Engine polled for changes to the active IPsec policy and detected no changes" });
    m.insert("4712", AuditEvent { category: "Policy Change", potential_criticality: "Low", summary: "IPsec Services encountered a potentially serious failure" });
    m.insert("4713", AuditEvent { category: "Policy Change", potential_criticality: "Medium", summary: "Kerberos policy was changed" });
    m.insert("4714", AuditEvent { category: "Policy Change", potential_criticality: "Medium", summary: "Encrypted data recovery policy was changed" });
    m.insert("4715", AuditEvent { category: "Policy Change", potential_criticality: "Medium", summary: "The audit policy (SACL) on an object was changed" });
    m.insert("4716", AuditEvent { category: "Policy Change", potential_criticality: "Medium", summary: "Trusted domain information was modified" });
    m.insert("4717", AuditEvent { category: "Policy Change", potential_criticality: "Low", summary: "System security access was granted to an account" });
    m.insert("4718", AuditEvent { category: "Policy Change", potential_criticality: "Low", summary: "System security access was removed from an account" });
    m.insert("4719", AuditEvent { category: "Policy Change", potential_criticality: "High", summary: "System audit policy was changed" });
    m.insert("4720", AuditEvent { category: "Account Management", potential_criticality: "Low", summary: "A user account was created" });
    m.insert("4722", AuditEvent { category: "Account Management", potential_criticality: "Low", summary: "A user account was enabled" });
    m.insert("4723", AuditEvent { category: "Account Management", potential_criticality: "Low", summary: "An attempt was made to change an account's password" });
    m.insert("4724", AuditEvent { category: "Account Management", potential_criticality: "Medium", summary: "An attempt was made to reset an account's password" });
    m.insert("4725", AuditEvent { category: "Account Management", potential_criticality: "Low", summary: "A user account was disabled" });
    m.insert("4726", AuditEvent { category: "Account Management", potential_criticality: "Low", summary: "A user account was deleted" });
    m.insert("4727", AuditEvent { category: "Account Management", potential_criticality: "Medium", summary: "A security-enabled global group was created" });
    m.insert("4728", AuditEvent { category: "Account Management", potential_criticality: "Low", summary: "A member was added to a security-enabled global group" });
    m.insert("4729", AuditEvent { category: "Account Management", potential_criticality: "Low", summary: "A member was removed from a security-enabled global group" });
    m.insert("4730", AuditEvent { category: "Account Management", potential_criticality: "Low", summary: "A security-enabled global group was deleted" });
    m.insert("4731", AuditEvent { category: "Account Management", potential_criticality: "Low", summary: "A security-enabled local group was created" });
    m.insert("4732", AuditEvent { category: "Account Management", potential_criticality: "Low", summary: "A member was added to a security-enabled local group" });
    m.insert("4733", AuditEvent { category: "Account Management", potential_criticality: "Low", summary: "A member was removed from a security-enabled local group" });
    m.insert("4734", AuditEvent { category: "Account Management", potential_criticality: "Low", summary: "A security-enabled local group was deleted" });
    m.insert("4735", AuditEvent { category: "Account Management", potential_criticality: "Medium", summary: "A security-enabled local group was changed" });
    m.insert("4737", AuditEvent { category: "Account Management", potential_criticality: "Medium", summary: "A security-enabled global group was changed" });
    m.insert("4738", AuditEvent { category: "Account Management", potential_criticality: "Low", summary: "A user account was changed" });
    m.insert("4739", AuditEvent { category: "Policy Change", potential_criticality: "Medium", summary: "Domain Policy was changed" });
    m.insert("4740", AuditEvent { category: "Account Management", potential_criticality: "Low", summary: "A user account was locked out" });
    m.insert("4741", AuditEvent { category: "Miscellaneous", potential_criticality: "Miscellaneous", summary: "A computer account was created" });
    m.insert("4742", AuditEvent { category: "Account Management", potential_criticality: "Low", summary: "A computer account was changed" });
    m.insert("4743", AuditEvent { category: "Account Management", potential_criticality: "Low", summary: "A computer account was deleted" });
    m.insert("4744", AuditEvent { category: "Account Management", potential_criticality: "Low", summary: "A security-disabled local group was created" });
    m.insert("4745", AuditEvent { category: "Account Management", potential_criticality: "Low", summary: "A security-disabled local group was changed" });
    m.insert("4746", AuditEvent { category: "Account Management", potential_criticality: "Low", summary: "A member was added to a security-disabled local group" });
    m.insert("4747", AuditEvent { category: "Account Management", potential_criticality: "Low", summary: "A member was removed from a security-disabled local group" });
    m.insert("4748", AuditEvent { category: "Account Management", potential_criticality: "Low", summary: "A security-disabled local group was deleted" });
    m.insert("4749", AuditEvent { category: "Account Management", potential_criticality: "Low", summary: "A security-disabled global group was created" });
    m.insert("4750", AuditEvent { category: "Account Management", potential_criticality: "Low", summary: "A security-disabled global group was changed" });
    m.insert("4751", AuditEvent { category: "Account Management", potential_criticality: "Low", summary: "A member was added to a security-disabled global group" });
    m.insert("4752", AuditEvent { category: "Account Management", potential_criticality: "Low", summary: "A member was removed from a security-disabled global group" });
    m.insert("4753", AuditEvent { category: "Account Management", potential_criticality: "Low", summary: "A security-disabled global group was deleted" });
    m.insert("4754", AuditEvent { category: "Account Management", potential_criticality: "Medium", summary: "A security-enabled universal group was created" });
    m.insert("4755", AuditEvent { category: "Account Management", potential_criticality: "Medium", summary: "A security-enabled universal group was changed" });
    m.insert("4756", AuditEvent { category: "Account Management", potential_criticality: "Low", summary: "A member was added to a security-enabled universal group" });
    m.insert("4757", AuditEvent { category: "Account Management", potential_criticality: "Low", summary: "A member was removed from a security-enabled universal group" });
    m.insert("4758", AuditEvent { category: "Account Management", potential_criticality: "Low", summary: "A security-enabled universal group was deleted" });
    m.insert("4759", AuditEvent { category: "Account Management", potential_criticality: "Low", summary: "A security-disabled universal group was created" });
    m.insert("4760", AuditEvent { category: "Account Management", potential_criticality: "Low", summary: "A security-disabled universal group was changed" });
    m.insert("4761", AuditEvent { category: "Account Management", potential_criticality: "Low", summary: "A member was added to a security-disabled universal group" });
    m.insert("4762", AuditEvent { category: "Account Management", potential_criticality: "Low", summary: "A member was removed from a security-disabled universal group" });
    m.insert("4763", AuditEvent { category: "Miscellaneous", potential_criticality: "Miscellaneous", summary: "A security-disabled universal group was deleted" });
    m.insert("4764", AuditEvent { category: "Account Management", potential_criticality: "Low", summary: "A group’s type was changed" });
    m.insert("4765", AuditEvent { category: "Account Management", potential_criticality: "High", summary: "SID History was added to an account" });
    m.insert("4766", AuditEvent { category: "Account Management", potential_criticality: "High", summary: "An attempt to add SID History to an account failed" });
    m.insert("4767", AuditEvent { category: "Account Management", potential_criticality: "Low", summary: "A user account was unlocked" });
    m.insert("4768", AuditEvent { category: "Account Logon", potential_criticality: "Low", summary: "A Kerberos authentication ticket (TGT) was requested" });
    m.insert("4769", AuditEvent { category: "Account Logon", potential_criticality: "Low", summary: "A Kerberos service ticket was requested" });
    m.insert("4770", AuditEvent { category: "Account Logon", potential_criticality: "Low", summary: "A Kerberos service ticket was renewed" });
    m.insert("4771", AuditEvent { category: "Account Logon", potential_criticality: "Low", summary: "Kerberos pre-authentication failed" });
    m.insert("4772", AuditEvent { category: "Account Logon", potential_criticality: "Low", summary: "A Kerberos authentication ticket request failed" });
    m.insert("4773", AuditEvent { category: "Account Logon", potential_criticality: "Low", summary: "A Kerberos service ticket request failed" });
    m.insert("4774", AuditEvent { category: "Account Logon", potential_criticality: "Low", summary: "An account was mapped for logon" });
    m.insert("4775", AuditEvent { category: "Account Logon", potential_criticality: "Low", summary: "An account could not be mapped for logon" });
    m.insert("4776", AuditEvent { category: "Account Logon", potential_criticality: "Low", summary: "The domain controller attempted to validate the credentials for an account" });
    m.insert("4777", AuditEvent { category: "Account Logon", potential_criticality: "Low", summary: "The domain controller failed to validate the credentials for an account" });
    m.insert("4778", AuditEvent { category: "Logon/Logoff", potential_criticality: "Low", summary: "A session was reconnected to a Window Station" });
    m.insert("4779", AuditEvent { category: "Logon/Logoff", potential_criticality: "Low", summary: "A session was disconnected from a Window Station" });
    m.insert("4780", AuditEvent { category: "Account Management", potential_criticality: "Medium", summary: "The ACL was set on accounts which are members of administrators groups" });
    m.insert("4781", AuditEvent { category: "Account Management", potential_criticality: "Low", summary: "The name of an account was changed" });
    m.insert("4782", AuditEvent { category: "Account Management", potential_criticality: "Low", summary: "The password hash an account was accessed" });
    m.insert("4783", AuditEvent { category: "Account Management", potential_criticality: "Low", summary: "A basic application group was created" });
    m.insert("4784", AuditEvent { category: "Account Management", potential_criticality: "Low", summary: "A basic application group was changed" });
    m.insert("4785", AuditEvent { category: "Account Management", potential_criticality: "Low", summary: "A member was added to a basic application group" });
    m.insert("4786", AuditEvent { category: "Account Management", potential_criticality: "Low", summary: "A member was removed from a basic application group" });
    m.insert("4787", AuditEvent { category: "Account Management", potential_criticality: "Low", summary: "A non-member was added to a basic application group" });
    m.insert("4788", AuditEvent { category: "Account Management", potential_criticality: "Low", summary: "A non-member was removed from a basic application group" });
    m.insert("4789", AuditEvent { category: "Account Management", potential_criticality: "Low", summary: "A basic application group was deleted" });
    m.insert("4790", AuditEvent { category: "Account Management", potential_criticality: "Low", summary: "An LDAP query group was created" });
    m.insert("4791", AuditEvent { category: "Account Management", potential_criticality: "Low", summary: "A basic application group was changed" });
    m.insert("4792", AuditEvent { category: "Account Management", potential_criticality: "Low", summary: "An LDAP query group was deleted" });
    m.insert("4793", AuditEvent { category: "Account Management", potential_criticality: "Low", summary: "The Password Policy Checking API was called" });
    m.insert("4794", AuditEvent { category: "Account Management", potential_criticality: "High", summary: "An attempt was made to set the Directory Services Restore Mode" });
    m.insert("4797", AuditEvent { category: "Account Management", potential_criticality: "Low", summary: "An attempt was made to query the existence of a blank password for an account" });
    m.insert("4798", AuditEvent { category: "Account Management", potential_criticality: "Low", summary: "A user's local group membership was enumerated" });
    m.insert("4799", AuditEvent { category: "Account Management", potential_criticality: "Low", summary: "A security-enabled local group membership was enumerated" });
    m.insert("4800", AuditEvent { category: "Logon/Logoff", potential_criticality: "Low", summary: "The workstation was locked" });
    m.insert("4801", AuditEvent { category: "Logon/Logoff", potential_criticality: "Low", summary: "The workstation was unlocked" });
    m.insert("4802", AuditEvent { category: "Logon/Logoff", potential_criticality: "Low", summary: "The screen saver was invoked" });
    m.insert("4803", AuditEvent { category: "Logon/Logoff", potential_criticality: "Low", summary: "The screen saver was dismissed" });
    m.insert("4816", AuditEvent { category: "System", potential_criticality: "Medium", summary: "RPC detected an integrity violation while decrypting an incoming message" });
    m.insert("4817", AuditEvent { category: "Policy Change", potential_criticality: "Low", summary: "Auditing settings on an object were changed" });
    m.insert("4818", AuditEvent { category: "Object Access", potential_criticality: "Low", summary: "Proposed Central Access Policy does not grant the same access permissions as the current Central Access Policy" });
    m.insert("4819", AuditEvent { category: "Policy Change", potential_criticality: "Low", summary: "Central Access Policies on the machine have been changed" });
    m.insert("4820", AuditEvent { category: "Account Logon", potential_criticality: "Low", summary: "A Kerberos Ticket-granting-ticket (TGT) was denied because the device does not meet the access control restrictions" });
    m.insert("4821", AuditEvent { category: "Account Logon", potential_criticality: "Low", summary: "A Kerberos service ticket was denied because the user, device, or both does not meet the access control restrictions" });
    m.insert("4822", AuditEvent { category: "Account Logon", potential_criticality: "Low", summary: "NTLM authentication failed because the account was a member of the Protected User group" });
    m.insert("4823", AuditEvent { category: "Account Logon", potential_criticality: "Low", summary: "NTLM authentication failed because access control restrictions are required" });
    m.insert("4824", AuditEvent { category: "Account Logon", potential_criticality: "Low", summary: "Kerberos preauthentication by using DES or RC4 failed because the account was a member of the Protected User group" });
    m.insert("4825", AuditEvent { category: "Logon/Logoff", potential_criticality: "Low", summary: "A user was denied the access to Remote Desktop" });
    m.insert("4826", AuditEvent { category: "Policy Change", potential_criticality: "Low", summary: "Boot Configuration Data loaded" });
    m.insert("4830", AuditEvent { category: "Miscellaneous", potential_criticality: "Miscellaneous", summary: "SID History was removed from an account" });
    m.insert("4864", AuditEvent { category: "Policy Change", potential_criticality: "Low", summary: "A namespace collision was detected" });
    m.insert("4865", AuditEvent { category: "Policy Change", potential_criticality: "Medium", summary: "A trusted forest information entry was added" });
    m.insert("4866", AuditEvent { category: "Policy Change", potential_criticality: "Medium", summary: "A trusted forest information entry was removed" });
    m.insert("4867", AuditEvent { category: "Policy Change", potential_criticality: "Medium", summary: "A trusted forest information entry was modified" });
    m.insert("4868", AuditEvent { category: "Object Access", potential_criticality: "Medium", summary: "The certificate manager denied a pending certificate request" });
    m.insert("4869", AuditEvent { category: "Object Access", potential_criticality: "Low", summary: "Certificate Services received a resubmitted certificate request" });
    m.insert("4870", AuditEvent { category: "Object Access", potential_criticality: "Medium", summary: "Certificate Services revoked a certificate" });
    m.insert("4871", AuditEvent { category: "Object Access", potential_criticality: "Low", summary: "Certificate Services received a request to publish the certificate revocation list (CRL)" });
    m.insert("4872", AuditEvent { category: "Object Access", potential_criticality: "Low", summary: "Certificate Services published the certificate revocation list (CRL)" });
    m.insert("4873", AuditEvent { category: "Object Access", potential_criticality: "Low", summary: "A certificate request extension changed" });
    m.insert("4874", AuditEvent { category: "Object Access", potential_criticality: "Low", summary: "One or more certificate request attributes changed" });
    m.insert("4875", AuditEvent { category: "Object Access", potential_criticality: "Low", summary: "Certificate Services received a request to shut down" });
    m.insert("4876", AuditEvent { category: "Object Access", potential_criticality: "Low", summary: "Certificate Services backup started" });
    m.insert("4877", AuditEvent { category: "Object Access", potential_criticality: "Low", summary: "Certificate Services backup completed" });
    m.insert("4878", AuditEvent { category: "Object Access", potential_criticality: "Low", summary: "Certificate Services restore started" });
    m.insert("4879", AuditEvent { category: "Object Access", potential_criticality: "Low", summary: "Certificate Services restore completed" });
    m.insert("4880", AuditEvent { category: "Object Access", potential_criticality: "Low", summary: "Certificate Services started" });
    m.insert("4881", AuditEvent { category: "Object Access", potential_criticality: "Low", summary: "Certificate Services stopped" });
    m.insert("4882", AuditEvent { category: "Object Access", potential_criticality: "Medium", summary: "The security permissions for Certificate Services changed" });
    m.insert("4883", AuditEvent { category: "Object Access", potential_criticality: "Low", summary: "Certificate Services retrieved an archived key" });
    m.insert("4884", AuditEvent { category: "Object Access", potential_criticality: "Low", summary: "Certificate Services imported a certificate into its database" });
    m.insert("4885", AuditEvent { category: "Object Access", potential_criticality: "Medium", summary: "The audit filter for Certificate Services changed" });
    m.insert("4886", AuditEvent { category: "Object Access", potential_criticality: "Low", summary: "Certificate Services received a certificate request" });
    m.insert("4887", AuditEvent { category: "Object Access", potential_criticality: "Low", summary: "Certificate Services approved a certificate request and issued a certificate" });
    m.insert("4888", AuditEvent { category: "Object Access", potential_criticality: "Low", summary: "Certificate Services denied a certificate request" });
    m.insert("4889", AuditEvent { category: "Object Access", potential_criticality: "Low", summary: "Certificate Services set the status of a certificate request to pending" });
    m.insert("4890", AuditEvent { category: "Object Access", potential_criticality: "Medium", summary: "The certificate manager settings for Certificate Services changed" });
    m.insert("4891", AuditEvent { category: "Object Access", potential_criticality: "Low", summary: "A configuration entry changed in Certificate Services" });
    m.insert("4892", AuditEvent { category: "Object Access", potential_criticality: "Medium", summary: "A property of Certificate Services changed" });
    m.insert("4893", AuditEvent { category: "Object Access", potential_criticality: "Low", summary: "Certificate Services archived a key" });
    m.insert("4894", AuditEvent { category: "Object Access", potential_criticality: "Low", summary: "Certificate Services imported and archived a key" });
    m.insert("4895", AuditEvent { category: "Object Access", potential_criticality: "Low", summary: "Certificate Services published the CA certificate to Active Directory Domain Services" });
    m.insert("4896", AuditEvent { category: "Object Access", potential_criticality: "Medium", summary: "One or more rows have been deleted from the certificate database" });
    m.insert("4897", AuditEvent { category: "Object Access", potential_criticality: "High", summary: "Role separation enabled" });
    m.insert("4898", AuditEvent { category: "Object Access", potential_criticality: "Low", summary: "Certificate Services loaded a template" });
    m.insert("4899", AuditEvent { category: "Object Access", potential_criticality: "Low", summary: "A Certificate Services template was updated" });
    m.insert("4900", AuditEvent { category: "Object Access", potential_criticality: "Low", summary: "Certificate Services template security was updated" });
    m.insert("4902", AuditEvent { category: "Policy Change", potential_criticality: "Low", summary: "The Per-user audit policy table was created" });
    m.insert("4904", AuditEvent { category: "Policy Change", potential_criticality: "Low", summary: "An attempt was made to register a security event source" });
    m.insert("4905", AuditEvent { category: "Policy Change", potential_criticality: "Low", summary: "An attempt was made to unregister a security event source" });
    m.insert("4906", AuditEvent { category: "Policy Change", potential_criticality: "Medium", summary: "The CrashOnAuditFail value has changed" });
    m.insert("4907", AuditEvent { category: "Policy Change", potential_criticality: "Medium", summary: "Auditing settings on object were changed" });
    m.insert("4908", AuditEvent { category: "Policy Change", potential_criticality: "Medium", summary: "Special Groups Logon table modified" });
    m.insert("4909", AuditEvent { category: "Policy Change", potential_criticality: "Low", summary: "The local policy settings for the TBS were changed" });
    m.insert("4910", AuditEvent { category: "Policy Change", potential_criticality: "Low", summary: "The group policy settings for the TBS were changed" });
    m.insert("4911", AuditEvent { category: "Policy Change", potential_criticality: "Low", summary: "Resource attributes of the object were changed" });
    m.insert("4912", AuditEvent { category: "Policy Change", potential_criticality: "Medium", summary: "Per User Audit Policy was changed" });
    m.insert("4913", AuditEvent { category: "Policy Change", potential_criticality: "Low", summary: "Central Access Policy on the object was changed" });
    m.insert("4928", AuditEvent { category: "DS Access", potential_criticality: "Low", summary: "An Active Directory replica source naming context was established" });
    m.insert("4929", AuditEvent { category: "DS Access", potential_criticality: "Low", summary: "An Active Directory replica source naming context was removed" });
    m.insert("4930", AuditEvent { category: "DS Access", potential_criticality: "Low", summary: "An Active Directory replica source naming context was modified" });
    m.insert("4931", AuditEvent { category: "DS Access", potential_criticality: "Low", summary: "An Active Directory replica destination naming context was modified" });
    m.insert("4932", AuditEvent { category: "DS Access", potential_criticality: "Low", summary: "Synchronization of a replica of an Active Directory naming context has begun" });
    m.insert("4933", AuditEvent { category: "DS Access", potential_criticality: "Low", summary: "Synchronization of a replica of an Active Directory naming context has ended" });
    m.insert("4934", AuditEvent { category: "DS Access", potential_criticality: "Low", summary: "Attributes of an Active Directory object were replicated" });
    m.insert("4935", AuditEvent { category: "DS Access", potential_criticality: "Low", summary: "Replication failure begins" });
    m.insert("4936", AuditEvent { category: "DS Access", potential_criticality: "Low", summary: "Replication failure ends" });
    m.insert("4937", AuditEvent { category: "DS Access", potential_criticality: "Low", summary: "A lingering object was removed from a replica" });
    m.insert("4944", AuditEvent { category: "Policy Change", potential_criticality: "Low", summary: "The following policy was active when the Windows Firewall started" });
    m.insert("4945", AuditEvent { category: "Policy Change", potential_criticality: "Low", summary: "A rule was listed when the Windows Firewall started" });
    m.insert("4946", AuditEvent { category: "Policy Change", potential_criticality: "Low", summary: "A change has been made to Windows Firewall exception list. A rule was added" });
    m.insert("4947", AuditEvent { category: "Policy Change", potential_criticality: "Low", summary: "A change has been made to Windows Firewall exception list. A rule was modified" });
    m.insert("4948", AuditEvent { category: "Policy Change", potential_criticality: "Low", summary: "A change has been made to Windows Firewall exception list. A rule was deleted" });
    m.insert("4949", AuditEvent { category: "Policy Change", potential_criticality: "Low", summary: "Windows Firewall settings were restored to the default values" });
    m.insert("4950", AuditEvent { category: "Policy Change", potential_criticality: "Low", summary: "A Windows Firewall setting has changed" });
    m.insert("4951", AuditEvent { category: "Policy Change", potential_criticality: "Low", summary: "A rule has been ignored because its major version number was not recognized by Windows Firewall" });
    m.insert("4952", AuditEvent { category: "Policy Change", potential_criticality: "Low", summary: "Parts of a rule have been ignored because its minor version number was not recognized by Windows Firewall. The other parts of the rule will be enforced" });
    m.insert("4953", AuditEvent { category: "Policy Change", potential_criticality: "Low", summary: "A rule has been ignored by Windows Firewall because it could not parse the rule" });
    m.insert("4954", AuditEvent { category: "Policy Change", potential_criticality: "Low", summary: "Windows Firewall Group Policy settings have changed. The new settings have been applied" });
    m.insert("4956", AuditEvent { category: "Policy Change", potential_criticality: "Low", summary: "Windows Firewall has changed the active profile" });
    m.insert("4957", AuditEvent { category: "Policy Change", potential_criticality: "Low", summary: "Windows Firewall did not apply the following rule" });
    m.insert("4958", AuditEvent { category: "Policy Change", potential_criticality: "Low", summary: "Windows Firewall did not apply the following rule because the rule referred to items not configured on this computer" });
    m.insert("4960", AuditEvent { category: "System", potential_criticality: "Medium", summary: "IPsec dropped an inbound packet that failed an integrity check. If this problem persists, it could indicate a network issue or that packets are being modified in transit to this computer. Verify that the packets sent from the remote computer are the same as those received by this computer. This error might also indicate interoperability problems with other IPsec implementations" });
    m.insert("4961", AuditEvent { category: "System", potential_criticality: "Medium", summary: "IPsec dropped an inbound packet that failed a replay check. If this problem persists, it could indicate a replay attack against this computer" });
    m.insert("4962", AuditEvent { category: "System", potential_criticality: "Medium", summary: "IPsec dropped an inbound packet that failed a replay check. The inbound packet had too low a sequence number to ensure it was not a replay" });
    m.insert("4963", AuditEvent { category: "System", potential_criticality: "Medium", summary: "IPsec dropped an inbound clear text packet that should have been secured. This is usually due to the remote computer changing its IPsec policy without informing this computer. This could also be a spoofing attack attempt" });
    m.insert("4964", AuditEvent { category: "Logon/Logoff", potential_criticality: "High", summary: "Special groups have been assigned to a new logon" });
    m.insert("4965", AuditEvent { category: "System", potential_criticality: "Medium", summary: "IPsec received a packet from a remote computer with an incorrect Security Parameter Index (SPI). This is usually caused by malfunctioning hardware that is corrupting packets. If these errors persist, verify that the packets sent from the remote computer are the same as those received by this computer. This error may also indicate interoperability problems with other IPsec implementations. In that case, if connectivity is not impeded, then these events can be ignored" });
    m.insert("4976", AuditEvent { category: "Logon/Logoff", potential_criticality: "Medium", summary: "During Main Mode negotiation, IPsec received an invalid negotiation packet. If this problem persists, it could indicate a network issue or an attempt to modify or replay this negotiation" });
    m.insert("4977", AuditEvent { category: "Logon/Logoff", potential_criticality: "Medium", summary: "During Quick Mode negotiation, IPsec received an invalid negotiation packet. If this problem persists, it could indicate a network issue or an attempt to modify or replay this negotiation" });
    m.insert("4978", AuditEvent { category: "Logon/Logoff", potential_criticality: "Medium", summary: "During Extended Mode negotiation, IPsec received an invalid negotiation packet. If this problem persists, it could indicate a network issue or an attempt to modify or replay this negotiation" });
    m.insert("4979", AuditEvent { category: "Logon/Logoff", potential_criticality: "Low", summary: "IPsec Main Mode and Extended Mode security associations were established" });
    m.insert("4980", AuditEvent { category: "Logon/Logoff", potential_criticality: "Low", summary: "IPsec Main Mode and Extended Mode security associations were established" });
    m.insert("4981", AuditEvent { category: "Logon/Logoff", potential_criticality: "Low", summary: "IPsec Main Mode and Extended Mode security associations were established" });
    m.insert("4982", AuditEvent { category: "Logon/Logoff", potential_criticality: "Low", summary: "IPsec Main Mode and Extended Mode security associations were established" });
    m.insert("4983", AuditEvent { category: "Logon/Logoff", potential_criticality: "Medium", summary: "An IPsec Extended Mode negotiation failed. The corresponding Main Mode security association has been deleted" });
    m.insert("4984", AuditEvent { category: "Logon/Logoff", potential_criticality: "Medium", summary: "An IPsec Extended Mode negotiation failed. The corresponding Main Mode security association has been deleted" });
    m.insert("4985", AuditEvent { category: "Object Access", potential_criticality: "Low", summary: "The state of a transaction has changed" });
    m.insert("5024", AuditEvent { category: "System", potential_criticality: "Low", summary: "The Windows Firewall Service has started successfully" });
    m.insert("5025", AuditEvent { category: "System", potential_criticality: "Low", summary: "The Windows Firewall Service has been stopped" });
    m.insert("5027", AuditEvent { category: "System", potential_criticality: "Medium", summary: "The Windows Firewall Service was unable to retrieve the security policy from the local storage. The service will continue enforcing the current policy" });
    m.insert("5028", AuditEvent { category: "System", potential_criticality: "Medium", summary: "The Windows Firewall Service was unable to parse the new security policy. The service will continue with currently enforced policy" });
    m.insert("5029", AuditEvent { category: "System", potential_criticality: "Medium", summary: "The Windows Firewall Service failed to initialize the driver. The service will continue to enforce the current policy" });
    m.insert("5030", AuditEvent { category: "System", potential_criticality: "Medium", summary: "The Windows Firewall Service failed to start" });
    m.insert("5031", AuditEvent { category: "Object Access", potential_criticality: "Low", summary: "The Windows Firewall Service blocked an application from accepting incoming connections on the network" });
    m.insert("5032", AuditEvent { category: "System", potential_criticality: "Low", summary: "Windows Firewall was unable to notify the user that it blocked an application from accepting incoming connections on the network" });
    m.insert("5033", AuditEvent { category: "System", potential_criticality: "Low", summary: "The Windows Firewall Driver has started successfully" });
    m.insert("5034", AuditEvent { category: "System", potential_criticality: "Low", summary: "The Windows Firewall Driver has been stopped" });
    m.insert("5035", AuditEvent { category: "System", potential_criticality: "Medium", summary: "The Windows Firewall Driver failed to start" });
    m.insert("5037", AuditEvent { category: "System", potential_criticality: "Medium", summary: "The Windows Firewall Driver detected critical runtime error. Terminating" });
    m.insert("5038", AuditEvent { category: "System", potential_criticality: "Medium", summary: "Code integrity determined that the image hash of a file is not valid. The file could be corrupt due to unauthorized modification or the invalid hash could indicate a potential disk device error" });
    m.insert("5039", AuditEvent { category: "Object Access", potential_criticality: "Low", summary: "A registry key was virtualized" });
    m.insert("5040", AuditEvent { category: "Policy Change", potential_criticality: "Low", summary: "A change has been made to IPsec settings. An Authentication Set was added" });
    m.insert("5041", AuditEvent { category: "Policy Change", potential_criticality: "Low", summary: "A change has been made to IPsec settings. An Authentication Set was modified" });
    m.insert("5042", AuditEvent { category: "Policy Change", potential_criticality: "Low", summary: "A change has been made to IPsec settings. An Authentication Set was deleted" });
    m.insert("5043", AuditEvent { category: "Policy Change", potential_criticality: "Low", summary: "A change has been made to IPsec settings. A Connection Security Rule was added" });
    m.insert("5044", AuditEvent { category: "Policy Change", potential_criticality: "Low", summary: "A change has been made to IPsec settings. A Connection Security Rule was modified" });
    m.insert("5045", AuditEvent { category: "Policy Change", potential_criticality: "Low", summary: "A change has been made to IPsec settings. A Connection Security Rule was deleted" });
    m.insert("5046", AuditEvent { category: "Policy Change", potential_criticality: "Low", summary: "A change has been made to IPsec settings. A Crypto Set was added" });
    m.insert("5047", AuditEvent { category: "Policy Change", potential_criticality: "Low", summary: "A change has been made to IPsec settings. A Crypto Set was modified" });
    m.insert("5048", AuditEvent { category: "Policy Change", potential_criticality: "Low", summary: "A change has been made to IPsec settings. A Crypto Set was deleted" });
    m.insert("5049", AuditEvent { category: "Logon/Logoff", potential_criticality: "Low", summary: "An IPsec Security Association was deleted" });
    m.insert("5050", AuditEvent { category: "System", potential_criticality: "Low", summary: "An attempt to programmatically disable the Windows Firewall was rejected because this API is not supported on Windows Vista" });
    m.insert("5051", AuditEvent { category: "Object Access", potential_criticality: "Low", summary: "A file was virtualized" });
    m.insert("5056", AuditEvent { category: "System", potential_criticality: "Low", summary: "A cryptographic self test was performed" });
    m.insert("5057", AuditEvent { category: "System", potential_criticality: "Low", summary: "A cryptographic primitive operation failed" });
    m.insert("5058", AuditEvent { category: "System", potential_criticality: "Low", summary: "Key file operation" });
    m.insert("5059", AuditEvent { category: "System", potential_criticality: "Low", summary: "Key migration operation" });
    m.insert("5060", AuditEvent { category: "System", potential_criticality: "Low", summary: "Verification operation failed" });
    m.insert("5061", AuditEvent { category: "System", potential_criticality: "Low", summary: "Cryptographic operation" });
    m.insert("5062", AuditEvent { category: "System", potential_criticality: "Low", summary: "A kernel-mode cryptographic self test was performed" });
    m.insert("5063", AuditEvent { category: "Policy Change", potential_criticality: "Low", summary: "A cryptographic provider operation was attempted" });
    m.insert("5064", AuditEvent { category: "Policy Change", potential_criticality: "Low", summary: "A cryptographic context operation was attempted" });
    m.insert("5065", AuditEvent { category: "Policy Change", potential_criticality: "Low", summary: "A cryptographic context modification was attempted" });
    m.insert("5066", AuditEvent { category: "Policy Change", potential_criticality: "Low", summary: "A cryptographic function operation was attempted" });
    m.insert("5067", AuditEvent { category: "Policy Change", potential_criticality: "Low", summary: "A cryptographic function modification was attempted" });
    m.insert("5068", AuditEvent { category: "Policy Change", potential_criticality: "Low", summary: "A cryptographic function provider operation was attempted" });
    m.insert("5069", AuditEvent { category: "Policy Change", potential_criticality: "Low", summary: "A cryptographic function property operation was attempted" });
    m.insert("5070", AuditEvent { category: "Policy Change", potential_criticality: "Low", summary: "A cryptographic function property modification was attempted" });
    m.insert("5071", AuditEvent { category: "System", potential_criticality: "Low", summary: "Key access denied by Microsoft key distribution service" });
    m.insert("5120", AuditEvent { category: "Object Access", potential_criticality: "Medium", summary: "OCSP Responder Service Started" });
    m.insert("5121", AuditEvent { category: "Object Access", potential_criticality: "Medium", summary: "OCSP Responder Service Stopped" });
    m.insert("5122", AuditEvent { category: "Object Access", potential_criticality: "Medium", summary: "A Configuration entry changed in the OCSP Responder Service" });
    m.insert("5123", AuditEvent { category: "Object Access", potential_criticality: "Medium", summary: "A configuration entry changed in the OCSP Responder Service" });
    m.insert("5124", AuditEvent { category: "Object Access", potential_criticality: "High", summary: "A security setting was updated on the OCSP Responder Service" });
    m.insert("5125", AuditEvent { category: "Object Access", potential_criticality: "Low", summary: "A request was submitted to the OCSP Responder Service" });
    m.insert("5126", AuditEvent { category: "Object Access", potential_criticality: "Low", summary: "Signing Certificate was automatically updated by the OCSP Responder Service" });
    m.insert("5127", AuditEvent { category: "Object Access", potential_criticality: "Low", summary: "The OCSP Revocation Provider successfully updated the revocation information" });
    m.insert("5136", AuditEvent { category: "DS Access", potential_criticality: "Low", summary: "A directory service object was modified" });
    m.insert("5137", AuditEvent { category: "DS Access", potential_criticality: "Low", summary: "A directory service object was created" });
    m.insert("5138", AuditEvent { category: "DS Access", potential_criticality: "Low", summary: "A directory service object was undeleted" });
    m.insert("5139", AuditEvent { category: "DS Access", potential_criticality: "Low", summary: "A directory service object was moved" });
    m.insert("5140", AuditEvent { category: "Object Access", potential_criticality: "Low", summary: "A network share object was accessed" });
    m.insert("5141", AuditEvent { category: "DS Access", potential_criticality: "Low", summary: "A directory service object was deleted" });
    m.insert("5142", AuditEvent { category: "Object Access", potential_criticality: "Low", summary: "A network share object was added" });
    m.insert("5143", AuditEvent { category: "Object Access", potential_criticality: "Low", summary: "A network share object was modified" });
    m.insert("5144", AuditEvent { category: "Object Access", potential_criticality: "Low", summary: "A network share object was deleted" });
    m.insert("5145", AuditEvent { category: "Object Access", potential_criticality: "Low", summary: "A network share object was checked to see whether the client can be granted desired access" });
    m.insert("5146", AuditEvent { category: "Object Access", potential_criticality: "Low", summary: "The Windows Filtering Platform has blocked a packet" });
    m.insert("5147", AuditEvent { category: "Object Access", potential_criticality: "Low", summary: "A more restrictive Windows Filtering Platform filter has blocked a packet" });
    m.insert("5148", AuditEvent { category: "Object Access", potential_criticality: "Low", summary: "The Windows Filtering Platform has detected a DoS attack and entered a defensive mode; packets associated with this attack will be discarded" });
    m.insert("5149", AuditEvent { category: "Object Access", potential_criticality: "Low", summary: "The DoS attack has subsided and normal processing is being resumed" });
    m.insert("5150", AuditEvent { category: "Object Access", potential_criticality: "Low", summary: "The Windows Filtering Platform has blocked a packet" });
    m.insert("5151", AuditEvent { category: "Object Access", potential_criticality: "Low", summary: "A more restrictive Windows Filtering Platform filter has blocked a packet" });
    m.insert("5152", AuditEvent { category: "Object Access", potential_criticality: "Low", summary: "The Windows Filtering Platform blocked a packet" });
    m.insert("5153", AuditEvent { category: "Object Access", potential_criticality: "Low", summary: "A more restrictive Windows Filtering Platform filter has blocked a packet" });
    m.insert("5154", AuditEvent { category: "Object Access", potential_criticality: "Low", summary: "The Windows Filtering Platform has permitted an application or service to listen on a port for incoming connections" });
    m.insert("5155", AuditEvent { category: "Object Access", potential_criticality: "Low", summary: "The Windows Filtering Platform has blocked an application or service from listening on a port for incoming connections" });
    m.insert("5156", AuditEvent { category: "Object Access", potential_criticality: "Low", summary: "The Windows Filtering Platform has allowed a connection" });
    m.insert("5157", AuditEvent { category: "Object Access", potential_criticality: "Low", summary: "The Windows Filtering Platform has blocked a connection" });
    m.insert("5158", AuditEvent { category: "Object Access", potential_criticality: "Low", summary: "The Windows Filtering Platform has permitted a bind to a local port" });
    m.insert("5159", AuditEvent { category: "Object Access", potential_criticality: "Low", summary: "The Windows Filtering Platform has blocked a bind to a local port" });
    m.insert("5168", AuditEvent { category: "Object Access", potential_criticality: "Low", summary: "Spn check for SMB/SMB2 failed" });
    m.insert("5169", AuditEvent { category: "DS Access", potential_criticality: "Low", summary: "A directory service object was modified" });
    m.insert("5170", AuditEvent { category: "Miscellaneous", potential_criticality: "Miscellaneous", summary: "A directory service object was modified during a background cleanup task" });
    m.insert("5376", AuditEvent { category: "Account Management", potential_criticality: "Medium", summary: "Credential Manager credentials were backed up" });
    m.insert("5377", AuditEvent { category: "Account Management", potential_criticality: "Medium", summary: "Credential Manager credentials were restored from a backup" });
    m.insert("5378", AuditEvent { category: "Logon/Logoff", potential_criticality: "Low", summary: "The requested credentials delegation was disallowed by policy" });
    m.insert("5379", AuditEvent { category: "Miscellaneous", potential_criticality: "Miscellaneous", summary: "Credential Manager credentials were read" });
    m.insert("5380", AuditEvent { category: "Miscellaneous", potential_criticality: "Miscellaneous", summary: "Vault Find Credential" });
    m.insert("5381", AuditEvent { category: "Miscellaneous", potential_criticality: "Miscellaneous", summary: "Vault credentials were read" });
    m.insert("5382", AuditEvent { category: "Miscellaneous", potential_criticality: "Miscellaneous", summary: "Vault credentials were read" });
    m.insert("5440", AuditEvent { category: "Policy Change", potential_criticality: "Low", summary: "The following callout was present when the Windows Filtering Platform Base Filtering Engine started" });
    m.insert("5441", AuditEvent { category: "Policy Change", potential_criticality: "Low", summary: "The following filter was present when the Windows Filtering Platform Base Filtering Engine started" });
    m.insert("5442", AuditEvent { category: "Policy Change", potential_criticality: "Low", summary: "The following provider was present when the Windows Filtering Platform Base Filtering Engine started" });
    m.insert("5443", AuditEvent { category: "Policy Change", potential_criticality: "Low", summary: "The following provider context was present when the Windows Filtering Platform Base Filtering Engine started" });
    m.insert("5444", AuditEvent { category: "Policy Change", potential_criticality: "Low", summary: "The following sub-layer was present when the Windows Filtering Platform Base Filtering Engine started" });
    m.insert("5446", AuditEvent { category: "Policy Change", potential_criticality: "Low", summary: "A Windows Filtering Platform callout has been changed" });
    m.insert("5447", AuditEvent { category: "Policy Change", potential_criticality: "Low", summary: "A Windows Filtering Platform filter has been changed" });
    m.insert("5448", AuditEvent { category: "Policy Change", potential_criticality: "Low", summary: "A Windows Filtering Platform provider has been changed" });
    m.insert("5449", AuditEvent { category: "Policy Change", potential_criticality: "Low", summary: "A Windows Filtering Platform provider context has been changed" });
    m.insert("5450", AuditEvent { category: "Policy Change", potential_criticality: "Low", summary: "A Windows Filtering Platform sub-layer has been changed" });
    m.insert("5451", AuditEvent { category: "Logon/Logoff", potential_criticality: "Low", summary: "An IPsec Quick Mode security association was established" });
    m.insert("5452", AuditEvent { category: "Logon/Logoff", potential_criticality: "Low", summary: "An IPsec Quick Mode security association ended" });
    m.insert("5453", AuditEvent { category: "Logon/Logoff", potential_criticality: "Medium", summary: "An IPsec negotiation with a remote computer failed because the IKE and AuthIP IPsec Keying Modules (IKEEXT) service is not started" });
    m.insert("5456", AuditEvent { category: "Policy Change", potential_criticality: "Low", summary: "PAStore Engine applied Active Directory storage IPsec policy on the computer" });
    m.insert("5457", AuditEvent { category: "Policy Change", potential_criticality: "Low", summary: "PAStore Engine failed to apply Active Directory storage IPsec policy on the computer" });
    m.insert("5458", AuditEvent { category: "Policy Change", potential_criticality: "Low", summary: "PAStore Engine applied locally cached copy of Active Directory storage IPsec policy on the computer" });
    m.insert("5459", AuditEvent { category: "Policy Change", potential_criticality: "Low", summary: "PAStore Engine failed to apply locally cached copy of Active Directory storage IPsec policy on the computer" });
    m.insert("5460", AuditEvent { category: "Policy Change", potential_criticality: "Low", summary: "PAStore Engine applied local registry storage IPsec policy on the computer" });
    m.insert("5461", AuditEvent { category: "Policy Change", potential_criticality: "Low", summary: "PAStore Engine failed to apply local registry storage IPsec policy on the computer" });
    m.insert("5462", AuditEvent { category: "Policy Change", potential_criticality: "Low", summary: "PAStore Engine failed to apply some rules of the active IPsec policy on the computer. Use the IP Security Monitor snap-in to diagnose the problem" });
    m.insert("5463", AuditEvent { category: "Policy Change", potential_criticality: "Low", summary: "PAStore Engine polled for changes to the active IPsec policy and detected no changes" });
    m.insert("5464", AuditEvent { category: "Policy Change", potential_criticality: "Low", summary: "PAStore Engine polled for changes to the active IPsec policy, detected changes, and applied them to IPsec Services" });
    m.insert("5465", AuditEvent { category: "Policy Change", potential_criticality: "Low", summary: "PAStore Engine received a control for forced reloading of IPsec policy and processed the control successfully" });
    m.insert("5466", AuditEvent { category: "Policy Change", potential_criticality: "Low", summary: "PAStore Engine polled for changes to the Active Directory IPsec policy, determined that Active Directory cannot be reached, and will use the cached copy of the Active Directory IPsec policy instead. Any changes made to the Active Directory IPsec policy since the last poll could not be applied" });
    m.insert("5467", AuditEvent { category: "Policy Change", potential_criticality: "Low", summary: "PAStore Engine polled for changes to the Active Directory IPsec policy, determined that Active Directory can be reached, and found no changes to the policy. The cached copy of the Active Directory IPsec policy is no longer being used" });
    m.insert("5468", AuditEvent { category: "Policy Change", potential_criticality: "Low", summary: "PAStore Engine polled for changes to the Active Directory IPsec policy, determined that Active Directory can be reached, found changes to the policy, and applied those changes. The cached copy of the Active Directory IPsec policy is no longer being used" });
    m.insert("5471", AuditEvent { category: "Policy Change", potential_criticality: "Low", summary: "PAStore Engine loaded local storage IPsec policy on the computer" });
    m.insert("5472", AuditEvent { category: "Policy Change", potential_criticality: "Low", summary: "PAStore Engine failed to load local storage IPsec policy on the computer" });
    m.insert("5473", AuditEvent { category: "Policy Change", potential_criticality: "Low", summary: "PAStore Engine loaded directory storage IPsec policy on the computer" });
    m.insert("5474", AuditEvent { category: "Policy Change", potential_criticality: "Low", summary: "PAStore Engine failed to load directory storage IPsec policy on the computer" });
    m.insert("5477", AuditEvent { category: "Policy Change", potential_criticality: "Low", summary: "PAStore Engine failed to add quick mode filter" });
    m.insert("5478", AuditEvent { category: "System", potential_criticality: "Low", summary: "IPsec Services has started successfully" });
    m.insert("5479", AuditEvent { category: "System", potential_criticality: "Low", summary: "IPsec Services has been shut down successfully. The shutdown of IPsec Services can put the computer at greater risk of network attack or expose the computer to potential security risks" });
    m.insert("5480", AuditEvent { category: "System", potential_criticality: "Medium", summary: "IPsec Services failed to get the complete list of network interfaces on the computer. This poses a potential security risk because some of the network interfaces may not get the protection provided by the applied IPsec filters. Use the IP Security Monitor snap-in to diagnose the problem" });
    m.insert("5483", AuditEvent { category: "System", potential_criticality: "Medium", summary: "IPsec Services failed to initialize RPC server. IPsec Services could not be started" });
    m.insert("5484", AuditEvent { category: "System", potential_criticality: "Medium", summary: "IPsec Services has experienced a critical failure and has been shut down. The shutdown of IPsec Services can put the computer at greater risk of network attack or expose the computer to potential security risks" });
    m.insert("5485", AuditEvent { category: "System", potential_criticality: "Medium", summary: "IPsec Services failed to process some IPsec filters on a plug-and-play event for network interfaces. This poses a potential security risk because some of the network interfaces may not get the protection provided by the applied IPsec filters. Use the IP Security Monitor snap-in to diagnose the problem" });
    m.insert("5632", AuditEvent { category: "Logon/Logoff", potential_criticality: "Low", summary: "A request was made to authenticate to a wireless network" });
    m.insert("5633", AuditEvent { category: "Logon/Logoff", potential_criticality: "Low", summary: "A request was made to authenticate to a wired network" });
    m.insert("5712", AuditEvent { category: "Detailed Tracking", potential_criticality: "Low", summary: "A Remote Procedure Call (RPC) was attempted" });
    m.insert("5827", AuditEvent { category: "Miscellaneous", potential_criticality: "Medium", summary: "The Netlogon service denied a vulnerable Netlogon secure channel connection from a machine account" });
    m.insert("5828", AuditEvent { category: "Miscellaneous", potential_criticality: "Medium", summary: "The Netlogon service denied a vulnerable Netlogon secure channel connection using a trust account" });
    m.insert("5888", AuditEvent { category: "Object Access", potential_criticality: "Low", summary: "An object in the COM+ Catalog was modified" });
    m.insert("5889", AuditEvent { category: "Object Access", potential_criticality: "Low", summary: "An object was deleted from the COM+ Catalog" });
    m.insert("5890", AuditEvent { category: "Object Access", potential_criticality: "Low", summary: "An object was added to the COM+ Catalog" });
    m.insert("6005", AuditEvent { category: "Miscellaneous", potential_criticality: "Miscellaneous", summary: "The event log service was started. Indicates the system startup" });
    m.insert("6006", AuditEvent { category: "Miscellaneous", potential_criticality: "Miscellaneous", summary: "The event log service was stopped. Indicates the proper system shutdown" });
    m.insert("6008", AuditEvent { category: "Miscellaneous", potential_criticality: "Low", summary: "The previous system shutdown was unexpected" });
    m.insert("6009", AuditEvent { category: "Miscellaneous", potential_criticality: "Miscellaneous", summary: "Indicates the Windows product name, version, build number, service pack number, and operating system type detected at boot time" });
    m.insert("6013", AuditEvent { category: "Miscellaneous", potential_criticality: "Miscellaneous", summary: "The system uptime in seconds" });
    m.insert("6144", AuditEvent { category: "Policy Change", potential_criticality: "Low", summary: "Security policy in the group policy objects has been applied successfully" });
    m.insert("6145", AuditEvent { category: "Policy Change", potential_criticality: "Medium", summary: "One or more errors occurred while processing security policy in the group policy objects" });
    m.insert("6272", AuditEvent { category: "Logon/Logoff", potential_criticality: "Low", summary: "Network Policy Server granted access to a user" });
    m.insert("6273", AuditEvent { category: "Logon/Logoff", potential_criticality: "Medium", summary: "Network Policy Server denied access to a user" });
    m.insert("6274", AuditEvent { category: "Logon/Logoff", potential_criticality: "Medium", summary: "Network Policy Server discarded the request for a user" });
    m.insert("6275", AuditEvent { category: "Logon/Logoff", potential_criticality: "Medium", summary: "Network Policy Server discarded the accounting request for a user" });
    m.insert("6276", AuditEvent { category: "Logon/Logoff", potential_criticality: "Medium", summary: "Network Policy Server quarantined a user" });
    m.insert("6277", AuditEvent { category: "Logon/Logoff", potential_criticality: "Medium", summary: "Network Policy Server granted access to a user but put it on probation because the host did not meet the defined health policy" });
    m.insert("6278", AuditEvent { category: "Logon/Logoff", potential_criticality: "Medium", summary: "Network Policy Server granted full access to a user because the host met the defined health policy" });
    m.insert("6279", AuditEvent { category: "Logon/Logoff", potential_criticality: "Medium", summary: "Network Policy Server locked the user account due to repeated failed authentication attempts" });
    m.insert("6280", AuditEvent { category: "Logon/Logoff", potential_criticality: "Medium", summary: "Network Policy Server unlocked the user account" });
    m.insert("6281", AuditEvent { category: "System", potential_criticality: "Low", summary: "Code Integrity determined that the page hashes of an image file are not valid. The file could be improperly signed without page hashes or corrupt due to unauthorized modification. The invalid hashes could indicate a potential disk device error" });
    m.insert("6400", AuditEvent { category: "System", potential_criticality: "Low", summary: "BranchCache: Received an incorrectly formatted response while discovering availability of content" });
    m.insert("6401", AuditEvent { category: "System", potential_criticality: "Low", summary: "BranchCache: Received invalid data from a peer. Data discarded" });
    m.insert("6402", AuditEvent { category: "System", potential_criticality: "Low", summary: "BranchCache: The message to the hosted cache offering it data is incorrectly formatted" });
    m.insert("6403", AuditEvent { category: "System", potential_criticality: "Low", summary: "BranchCache: The hosted cache sent an incorrectly formatted response to the client's message to offer it data" });
    m.insert("6404", AuditEvent { category: "System", potential_criticality: "Low", summary: "BranchCache: Hosted cache could not be authenticated using the provisioned SSL certificate" });
    m.insert("6405", AuditEvent { category: "System", potential_criticality: "Low", summary: "BranchCache: %2 instance(s) of event id %1 occurred" });
    m.insert("6406", AuditEvent { category: "System", potential_criticality: "Low", summary: "%1 registered to Windows Firewall to control filtering for the following: %2" });
    m.insert("6407", AuditEvent { category: "System", potential_criticality: "Low", summary: "%1" });
    m.insert("6408", AuditEvent { category: "System", potential_criticality: "Low", summary: "Registered product %1 failed and Windows Firewall is now controlling the filtering for %2" });
    m.insert("6409", AuditEvent { category: "System", potential_criticality: "Low", summary: "BranchCache: A service connection point object could not be parsed" });
    m.insert("6410", AuditEvent { category: "System", potential_criticality: "Low", summary: "Code integrity determined that a file does not meet the security requirements to load into a process. This could be due to the use of shared sections or other issues" });
    m.insert("6416", AuditEvent { category: "System", potential_criticality: "Low", summary: "A new external device was recognized by the system" });
    m.insert("6417", AuditEvent { category: "System", potential_criticality: "Low", summary: "The FIPS mode crypto selftests succeeded" });
    m.insert("6418", AuditEvent { category: "System", potential_criticality: "Low", summary: "The FIPS mode crypto selftests failed" });
    m.insert("6419", AuditEvent { category: "System", potential_criticality: "Low", summary: "A request was made to disable a device" });
    m.insert("6420", AuditEvent { category: "System", potential_criticality: "Low", summary: "A device was disabled" });
    m.insert("6421", AuditEvent { category: "System", potential_criticality: "Low", summary: "A request was made to enable a device" });
    m.insert("6422", AuditEvent { category: "System", potential_criticality: "Low", summary: "A device was enabled" });
    m.insert("6423", AuditEvent { category: "System", potential_criticality: "Low", summary: "The installation of this device is forbidden by system policy" });
    m.insert("6424", AuditEvent { category: "System", potential_criticality: "Low", summary: "The installation of this device was allowed, after having previously been forbidden by policy" });
    m.insert("7009", AuditEvent { category: "Miscellaneous", potential_criticality: "Miscellaneous", summary: "a service timeout has occurred" });
    m.insert("7011", AuditEvent { category: "Miscellaneous", potential_criticality: "Miscellaneous", summary: "a service did not respond within the specified time" });
    m.insert("7023", AuditEvent { category: "Miscellaneous", potential_criticality: "Miscellaneous", summary: "the service terminated with the following error: The service terminated with the following service-specific error: Incorrect function" });
    m.insert("7024", AuditEvent { category: "Miscellaneous", potential_criticality: "Miscellaneous", summary: "the service terminated with service-specific error" });
    m.insert("7031", AuditEvent { category: "Miscellaneous", potential_criticality: "Miscellaneous", summary: "a service has stopped unexpectedly" });
    m.insert("7035", AuditEvent { category: "Miscellaneous", potential_criticality: "Miscellaneous", summary: "a service was successfully sent a start/Stop control" });
    m.insert("7036", AuditEvent { category: "Miscellaneous", potential_criticality: "Miscellaneous", summary: "a service has entered the running or stopped state" });
    m.insert("8191", AuditEvent { category: "Miscellaneous", potential_criticality: "Miscellaneous", summary: "Highest System-Defined Audit Message Value" });
    m.insert("11000", AuditEvent { category: "Miscellaneous", potential_criticality: "Miscellaneous", summary: "DHCPv6 Solicit" });
    m.insert("11001", AuditEvent { category: "Miscellaneous", potential_criticality: "Miscellaneous", summary: "DHCPv6 Advertise" });
    m.insert("11002", AuditEvent { category: "Miscellaneous", potential_criticality: "Miscellaneous", summary: "DHCPv6 Request" });
    m.insert("11003", AuditEvent { category: "Miscellaneous", potential_criticality: "Miscellaneous", summary: "DHCPv6 Confirm" });
    m.insert("11004", AuditEvent { category: "Miscellaneous", potential_criticality: "Miscellaneous", summary: "DHCPv6 Renew" });
    m.insert("11005", AuditEvent { category: "Miscellaneous", potential_criticality: "Miscellaneous", summary: "DHCPv6 Rebind" });
    m.insert("11006", AuditEvent { category: "Miscellaneous", potential_criticality: "Miscellaneous", summary: "DHCPv6 Decline" });
    m.insert("11007", AuditEvent { category: "Miscellaneous", potential_criticality: "Miscellaneous", summary: "DHCPv6 Release" });
    m.insert("11008", AuditEvent { category: "Miscellaneous", potential_criticality: "Miscellaneous", summary: "DHCPv6 Information Request" });
    m.insert("11009", AuditEvent { category: "Miscellaneous", potential_criticality: "Miscellaneous", summary: "DHCPv6 Scope Full" });
    m.insert("11010", AuditEvent { category: "Miscellaneous", potential_criticality: "Miscellaneous", summary: "DHCPv6 Started" });
    m.insert("11011", AuditEvent { category: "Miscellaneous", potential_criticality: "Miscellaneous", summary: "DHCPv6 Stopped" });
    m.insert("11012", AuditEvent { category: "Miscellaneous", potential_criticality: "Miscellaneous", summary: "DHCPv6 Audit log paused" });
    m.insert("11013", AuditEvent { category: "Miscellaneous", potential_criticality: "Miscellaneous", summary: "DHCPv6 Log File" });
    m.insert("11014", AuditEvent { category: "Miscellaneous", potential_criticality: "Miscellaneous", summary: "DHCPv6 Bad Address" });
    m.insert("11015", AuditEvent { category: "Miscellaneous", potential_criticality: "Miscellaneous", summary: "DHCPv6 Address is already in use" });
    m.insert("11016", AuditEvent { category: "Miscellaneous", potential_criticality: "Miscellaneous", summary: "DHCPv6 Client deleted" });
    m.insert("11017", AuditEvent { category: "Miscellaneous", potential_criticality: "Miscellaneous", summary: "DHCPv6 DNS record not deleted" });
    m.insert("11018", AuditEvent { category: "Miscellaneous", potential_criticality: "Miscellaneous", summary: "DHCPv6 Expired" });
    m.insert("11019", AuditEvent { category: "Miscellaneous", potential_criticality: "Miscellaneous", summary: "DHCPv6 Leases Expired and Leases Deleted" });
    m.insert("11020", AuditEvent { category: "Miscellaneous", potential_criticality: "Miscellaneous", summary: "DHCPv6 Database cleanup begin" });
    m.insert("11021", AuditEvent { category: "Miscellaneous", potential_criticality: "Miscellaneous", summary: "DHCPv6 Database cleanup end" });
    m.insert("11022", AuditEvent { category: "Miscellaneous", potential_criticality: "Miscellaneous", summary: "DNS IPv6 Update Request" });
    m.insert("11023", AuditEvent { category: "Miscellaneous", potential_criticality: "Miscellaneous", summary: "DNS IPv6 Update Failed" });
    m.insert("11024", AuditEvent { category: "Miscellaneous", potential_criticality: "Miscellaneous", summary: "DNS IPv6 Update Successful" });
    m.insert("11028", AuditEvent { category: "Miscellaneous", potential_criticality: "Miscellaneous", summary: "DNS IPv6 update request failed as the DNS update request queue limit exceeded" });
    m.insert("11029", AuditEvent { category: "Miscellaneous", potential_criticality: "Miscellaneous", summary: "DNS IPv6 update request failed" });
    m.insert("11030", AuditEvent { category: "Miscellaneous", potential_criticality: "Miscellaneous", summary: "DHCPv6 stateless client records purged" });
    m.insert("11031", AuditEvent { category: "Miscellaneous", potential_criticality: "Miscellaneous", summary: "DHCPv6 stateless client record is purged as the purge interval has expired for this client record" });
    m.insert("11032", AuditEvent { category: "Miscellaneous", potential_criticality: "Miscellaneous", summary: "DHCPV6 Information Request from IPV6 Stateless Client" });
    m.insert("24577", AuditEvent { category: "Miscellaneous", potential_criticality: "Low", summary: "Encryption of volume started" });
    m.insert("24578", AuditEvent { category: "Miscellaneous", potential_criticality: "Low", summary: "Encryption of volume stopped" });
    m.insert("24579", AuditEvent { category: "Miscellaneous", potential_criticality: "Low", summary: "Encryption of volume completed" });
    m.insert("24580", AuditEvent { category: "Miscellaneous", potential_criticality: "Low", summary: "Decryption of volume started" });
    m.insert("24581", AuditEvent { category: "Miscellaneous", potential_criticality: "Low", summary: "Decryption of volume stopped" });
    m.insert("24582", AuditEvent { category: "Miscellaneous", potential_criticality: "Low", summary: "Decryption of volume completed" });
    m.insert("24583", AuditEvent { category: "Miscellaneous", potential_criticality: "Low", summary: "Conversion worker thread for volume started" });
    m.insert("24584", AuditEvent { category: "Miscellaneous", potential_criticality: "Low", summary: "Conversion worker thread for volume temporarily stopped" });
    m.insert("24586", AuditEvent { category: "Miscellaneous", potential_criticality: "Medium", summary: "An error was encountered converting volume" });
    m.insert("24588", AuditEvent { category: "Miscellaneous", potential_criticality: "Low", summary: "The conversion operation on volume %2 encountered a bad sector error. Please validate the data on this volume" });
    m.insert("24592", AuditEvent { category: "Miscellaneous", potential_criticality: "Medium", summary: "An attempt to automatically restart conversion on volume %2 failed" });
    m.insert("24593", AuditEvent { category: "Miscellaneous", potential_criticality: "Medium", summary: "Metadata write: Volume %2 returning errors while trying to modify metadata. If failures continue, decrypt volume" });
    m.insert("24594", AuditEvent { category: "Miscellaneous", potential_criticality: "Medium", summary: "Metadata rebuild: An attempt to write a copy of metadata on volume %2 failed and may appear as disk corruption. If failures continue, decrypt volume" });
    m.insert("24595", AuditEvent { category: "Miscellaneous", potential_criticality: "Low", summary: "Volume %2 contains bad clusters. These clusters will be skipped during conversion" });
    m.insert("24621", AuditEvent { category: "Miscellaneous", potential_criticality: "Low", summary: "Initial state check: Rolling volume conversion transaction on %2" });
    m
});
