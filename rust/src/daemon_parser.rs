use std::collections::HashMap;
use std::fs::File;
use std::io::{<PERSON><PERSON><PERSON><PERSON>, <PERSON>uf<PERSON>eader};
use rayon::prelude::*;
use serde::{Deserialize, Serialize};

use crate::utils_classes::{DaemonConfig, MYSQLValue};
use crate::utils_parsers::{parse_ln, ConfigType};

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct ParsedResult {
    pub sensor_name: String,
    pub rows: Vec<Vec<String>>,
    pub daemon_errors_count: i32,
    pub daemon_warnings_count: i32,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct ParseResults {
    pub results: HashMap<String, ParsedResult>,
    pub total_lines_processed: usize,
    pub total_valid_lines: usize,
    pub processing_time_ms: u128,
}

/// High-performance daemon log parsing function
/// 
/// This function reads a log file and parses it using Rust's parallel processing
/// capabilities, providing significant performance improvements over Python.
/// 
/// # Arguments
/// * `log_file_path` - Path to the log file to parse
/// * `sensor_list_of_names_and_addresses` - List of sensor names and addresses
/// * `sensor_dict_of_addresses_and_names` - Dictionary mapping addresses to names
/// * `already_accomplished` - List of sensor names that have already been processed
/// 
/// # Returns
/// * `ParseResults` - Structured results containing parsed data for each sensor
pub fn parse_daemon_log_file(
    log_file_path: &str,
    sensor_list_of_names_and_addresses: &[String],
    sensor_dict_of_addresses_and_names: &HashMap<String, String>,
    already_accomplished: &[String],
) -> Result<ParseResults, Box<dyn std::error::Error>> {
    let start_time = std::time::Instant::now();
    
    // Read file into memory for parallel processing
    let file = File::open(log_file_path)?;
    let reader = BufReader::new(file);
    let lines: Vec<String> = reader.lines().collect::<Result<Vec<_>, _>>()?;
    
    let total_lines_processed = lines.len();
    
    // Parse lines in parallel using Rayon
    let parsed_lines: Vec<(Option<String>, Option<Vec<String>>)> = lines
        .par_iter()
        .map(|line| {
            parse_line_daemon(
                line.trim(),
                sensor_list_of_names_and_addresses,
                sensor_dict_of_addresses_and_names,
                already_accomplished,
            )
        })
        .collect();
    
    // Group results by sensor name
    let mut sensor_results: HashMap<String, ParsedResult> = HashMap::new();
    let mut total_valid_lines = 0;
    
    for (sensor_name_opt, parsed_line_opt) in parsed_lines {
        if let (Some(sensor_name), Some(parsed_line)) = (sensor_name_opt, parsed_line_opt) {
            total_valid_lines += 1;
            
            let result = sensor_results.entry(sensor_name.clone()).or_insert_with(|| {
                ParsedResult {
                    sensor_name: sensor_name.clone(),
                    rows: Vec::new(),
                    daemon_errors_count: 0,
                    daemon_warnings_count: 0,
                }
            });
            
            result.rows.push(parsed_line.clone());
            
            // Count errors and warnings based on the event type (index 2)
            if parsed_line.len() > 2 {
                let event_type = &parsed_line[2];
                if is_critical_event_type(event_type) {
                    result.daemon_errors_count += 1;
                } else if is_warning_event_type(event_type) {
                    result.daemon_warnings_count += 1;
                }
            }
        }
    }
    
    let processing_time_ms = start_time.elapsed().as_millis();
    
    Ok(ParseResults {
        results: sensor_results,
        total_lines_processed,
        total_valid_lines,
        processing_time_ms,
    })
}

/// Parse a single line for daemon logs
fn parse_line_daemon(
    line: &str,
    sensor_list_of_names_and_addresses: &[String],
    sensor_dict_of_addresses_and_names: &HashMap<String, String>,
    already_accomplished: &[String],
) -> (Option<String>, Option<Vec<String>>) {
    let (sensor_name_opt, parsed_line_opt) = parse_ln(
        line,
        ConfigType::Daemon,
        sensor_list_of_names_and_addresses,
        sensor_dict_of_addresses_and_names,
    );
    
    // Check if sensor is already accomplished
    if let Some(ref sensor_name) = sensor_name_opt {
        if already_accomplished.contains(sensor_name) {
            return (None, None);
        }
    }
    
    (sensor_name_opt, parsed_line_opt)
}

/// Check if an event type is critical (error)
fn is_critical_event_type(event_type: &str) -> bool {
    // Get critical event types from DaemonConfig
    if let MYSQLValue::List(criticals) = DaemonConfig::EVENT_TYPES__CRITICALS.value() {
        criticals.contains(&event_type.to_string())
    } else {
        // Fallback to pattern matching
        event_type.contains("/alert") ||
        event_type.contains("/crit") ||
        event_type.contains("/emerg") ||
        event_type.contains("/err")
    }
}

/// Check if an event type is warning
fn is_warning_event_type(event_type: &str) -> bool {
    // Get warning event types from DaemonConfig
    if let MYSQLValue::List(warnings) = DaemonConfig::EVENT_TYPES__WARNINGS.value() {
        warnings.contains(&event_type.to_string())
    } else {
        // Fallback to pattern matching
        event_type.contains("/warning")
    }
}

/// JSON wrapper for the daemon log parsing function
/// This can be called from Python via subprocess or FFI
pub fn parse_daemon_log_file_json(
    log_file_path: &str,
    sensor_list_of_names_and_addresses: &[String],
    sensor_dict_of_addresses_and_names: &HashMap<String, String>,
    already_accomplished: &[String],
) -> Result<String, Box<dyn std::error::Error>> {
    let results = parse_daemon_log_file(
        log_file_path,
        sensor_list_of_names_and_addresses,
        sensor_dict_of_addresses_and_names,
        already_accomplished,
    )?;

    Ok(serde_json::to_string(&results)?)
}

#[cfg(test)]
mod tests {
    use super::*;
    
    #[test]
    fn test_is_critical_event_type() {
        assert!(is_critical_event_type("(daemon/err)"));
        assert!(is_critical_event_type("(daemon/crit)"));
        assert!(is_critical_event_type("(daemon/alert)"));
        assert!(is_critical_event_type("(daemon/emerg)"));
        assert!(!is_critical_event_type("(daemon/info)"));
        assert!(!is_critical_event_type("(daemon/warning)"));
    }
    
    #[test]
    fn test_is_warning_event_type() {
        assert!(is_warning_event_type("(daemon/warning)"));
        assert!(!is_warning_event_type("(daemon/err)"));
        assert!(!is_warning_event_type("(daemon/info)"));
    }
}
