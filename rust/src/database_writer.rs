use std::fs::File;
use std::io::Write;
use std::path::Path;
use rayon::prelude::*;
use serde::{Deserialize, Serialize};

use crate::utils_classes::{MYSQLConfig, MYSQLValue};
use crate::daemon_parser::ParseResults;

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct DatabaseWriteResults {
    pub total_rows_written: usize,
    pub total_files_created: usize,
    pub processing_time_ms: u128,
    pub infile_paths: Vec<String>,
}

/// Simple database writer for parsed log data
/// 
/// This function takes parsed results and writes them to MySQL-compatible infiles,
/// then executes LOAD DATA INFILE statements to insert the data into the database.
/// 
/// # Arguments
/// * `parse_results` - Results from the daemon parser
/// * `table_name` - Name of the MySQL table to insert into
/// * `db_keys` - Column names for the database (excluding ID)
/// * `temp_dir` - Directory to create temporary infiles
/// 
/// # Returns
/// * `DatabaseWriteResults` - Results of the database write operation
pub fn write_to_database(
    parse_results: &ParseResults,
    table_name: &str,
    _db_keys: &str,
    temp_dir: &str,
) -> Result<DatabaseWriteResults, Box<dyn std::error::Error>> {
    let start_time = std::time::Instant::now();
    
    // Get MySQL configuration values
    let terminated_by = if let MYSQLValue::Str(val) = MYSQLConfig::TERMINATED_BY.value() {
        val
    } else {
        return Err("Invalid TERMINATED_BY configuration".into());
    };
    
    let enclosed_by = if let MYSQLValue::Str(val) = MYSQLConfig::ENCLOSED_BY.value() {
        val
    } else {
        return Err("Invalid ENCLOSED_BY configuration".into());
    };
    
    let chunk_size = if let MYSQLValue::Int(val) = MYSQLConfig::INFILE_CHUNKSIZE.value() {
        val as usize
    } else {
        return Err("Invalid INFILE_CHUNKSIZE configuration".into());
    };
    
    // Collect all rows from all sensors
    let mut all_rows: Vec<Vec<String>> = Vec::new();
    for (_, sensor_result) in &parse_results.results {
        all_rows.extend(sensor_result.rows.clone());
    }
    
    let total_rows = all_rows.len();
    if total_rows == 0 {
        return Ok(DatabaseWriteResults {
            total_rows_written: 0,
            total_files_created: 0,
            processing_time_ms: start_time.elapsed().as_millis(),
            infile_paths: Vec::new(),
        });
    }
    
    // Create chunks and write infiles in parallel
    let chunks: Vec<_> = all_rows.chunks(chunk_size).collect();
    let infile_paths: Vec<String> = chunks
        .par_iter()
        .enumerate()
        .map(|(chunk_index, chunk)| {
            let infile_path = format!("{}/infile_{}_{}.txt", temp_dir, table_name, chunk_index);
            
            // Write chunk to infile
            if let Err(e) = write_chunk_to_infile(
                chunk,
                &infile_path,
                &terminated_by,
                &enclosed_by,
                chunk_index * chunk_size + 1, // Starting row ID
            ) {
                eprintln!("Error writing chunk {}: {}", chunk_index, e);
                return String::new(); // Return empty string on error
            }
            
            infile_path
        })
        .filter(|path| !path.is_empty()) // Filter out failed writes
        .collect();
    
    let processing_time_ms = start_time.elapsed().as_millis();
    
    Ok(DatabaseWriteResults {
        total_rows_written: total_rows,
        total_files_created: infile_paths.len(),
        processing_time_ms,
        infile_paths,
    })
}

/// Write a chunk of rows to an infile
fn write_chunk_to_infile(
    chunk: &[Vec<String>],
    infile_path: &str,
    terminated_by: &str,
    enclosed_by: &str,
    starting_row_id: usize,
) -> Result<(), Box<dyn std::error::Error>> {
    let mut file = File::create(infile_path)?;
    
    for (index, row) in chunk.iter().enumerate() {
        let row_id = starting_row_id + index;
        
        // Format: ID + row data, each field enclosed and terminated
        let mut formatted_row = Vec::new();
        
        // Add ID as first field
        formatted_row.push(format!("{}{}{}", enclosed_by, row_id, enclosed_by));
        
        // Add each field from the row
        for field in row {
            formatted_row.push(format!("{}{}{}", enclosed_by, field, enclosed_by));
        }
        
        // Join with terminator and write line
        let line = format!("{}\n", formatted_row.join(terminated_by));
        file.write_all(line.as_bytes())?;
    }
    
    file.flush()?;
    Ok(())
}

/// Execute LOAD DATA INFILE statements for the created infiles
/// 
/// This function connects to MySQL and executes the LOAD DATA INFILE statements
/// to insert the data from the temporary files into the database.
/// 
/// # Arguments
/// * `infile_paths` - Paths to the infiles to load
/// * `table_name` - Name of the MySQL table
/// * `db_keys` - Column names for the database (excluding ID)
/// 
/// # Returns
/// * `Result<(), Box<dyn std::error::Error>>` - Success or error
pub fn execute_load_data_infile(
    infile_paths: &[String],
    table_name: &str,
    db_keys: &str,
) -> Result<(), Box<dyn std::error::Error>> {
    use mysql::*;
    use mysql::prelude::*;
    
    // Get MySQL configuration
    let host = std::env::var("MYSQL_HOST")?;
    let user = std::env::var("MYSQL_MASTER")?;
    let password = std::env::var("MYSQL_MASTER_PASSWD")?;
    
    let url = format!("mysql://{}:{}@{}", user, password, host);
    let pool = Pool::new(url.as_str())?;
    let mut conn = pool.get_conn()?;
    
    let terminated_by = if let MYSQLValue::Str(val) = MYSQLConfig::TERMINATED_BY.value() {
        val
    } else {
        return Err("Invalid TERMINATED_BY configuration".into());
    };
    
    let enclosed_by = if let MYSQLValue::Str(val) = MYSQLConfig::ENCLOSED_BY.value() {
        val
    } else {
        return Err("Invalid ENCLOSED_BY configuration".into());
    };
    
    let infile_statement = MYSQLConfig::get_infile_statement();
    
    // Process each infile
    for infile_path in infile_paths {
        if !Path::new(infile_path).exists() {
            continue;
        }
        
        // Set MySQL options for performance
        conn.query_drop("SET UNIQUE_CHECKS=0")?;
        conn.query_drop("SET FOREIGN_KEY_CHECKS=0")?;
        conn.query_drop("START TRANSACTION")?;
        
        // Execute LOAD DATA INFILE
        let load_query = format!(
            r#"{} "{}"
            INTO TABLE {}
            FIELDS TERMINATED BY "{}"
            ENCLOSED BY '{}'
            LINES TERMINATED BY "\n"
            (ID,{})"#,
            infile_statement,
            infile_path,
            table_name,
            terminated_by,
            enclosed_by,
            db_keys
        );
        
        conn.query_drop(load_query)?;
    }
    
    // Commit all transactions
    conn.query_drop("COMMIT")?;
    
    Ok(())
}

/// Clean up temporary infiles
pub fn cleanup_infiles(infile_paths: &[String]) -> Result<(), Box<dyn std::error::Error>> {
    for infile_path in infile_paths {
        if Path::new(infile_path).exists() {
            std::fs::remove_file(infile_path)?;
        }
    }
    Ok(())
}

/// Complete database write operation: create infiles, load data, and cleanup
/// 
/// This is the main function that combines all steps of the database writing process.
/// 
/// # Arguments
/// * `parse_results` - Results from the daemon parser
/// * `table_name` - Name of the MySQL table to insert into
/// * `db_keys` - Column names for the database (excluding ID)
/// * `temp_dir` - Directory to create temporary infiles
/// 
/// # Returns
/// * `DatabaseWriteResults` - Results of the database write operation
pub fn write_and_load_to_database(
    parse_results: &ParseResults,
    table_name: &str,
    db_keys: &str,
    temp_dir: &str,
) -> Result<DatabaseWriteResults, Box<dyn std::error::Error>> {
    // Step 1: Write infiles
    let write_results = write_to_database(parse_results, table_name, db_keys, temp_dir)?;
    
    if write_results.total_files_created == 0 {
        return Ok(write_results);
    }
    
    // Step 2: Load data into database
    execute_load_data_infile(&write_results.infile_paths, table_name, db_keys)?;
    
    // Step 3: Cleanup infiles
    cleanup_infiles(&write_results.infile_paths)?;
    
    Ok(write_results)
}
