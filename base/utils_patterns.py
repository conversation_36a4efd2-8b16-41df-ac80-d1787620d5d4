from re import compile


NSLOOKUP_NAME_PATTERN = compile(r'.+name = (.+)')


## NOTE: prepending .* to made it too slow
## 2023-05-13 09:51:58 Sensor-1
INIT_REG  = r'^(\d{4}-\d{2}-\d{2})\s+(\d{2}:\d{2}:\d{2})\s+(\S+)'

##               (auth/info)
EVENT_REG = r'\s+(\([^)]+\))'

##               [(squid-1)]
ALERT_REG = r'\s+(\[[^\]]+\])'

## 2000-01-01 00:00:00 Sensor-1 (daemon/info) [charon] 11[ENC] <con2|20> parsed INFORMATIONAL response 71 [ ]
DAEMON_PATTERN = compile(INIT_REG + EVENT_REG + ALERT_REG + r'\s+(.*)')

## JUMP_1 since we moved to nxlog, dns and dhcp entries look different, in that:
##        - they contain more info in the middle of the line, e.g.:
##          [MSWinEventLog 1N/A 42873 Fri] Sep 22 23:59:58 2023 N/A N/A N/A N/A N/A N/A N/A
##        - they contain a \tN/A at the very end of line
##
## 2000-01-01 00:00:00 WindowsServer-1 (user/info) [MSWinEventLog	1	N/A	229692	Sun] Dec 08 20:01:30 2024	N/A	N/A	N/A	N/A	N/A	N/A	N/A		[dhcp] 11,12/08/24,20:01:29,Renew,192.168.0.0,xPhone.sth.local,38A4EDBA48B9,,640399471,0,,,,0x616E64726F69642D646863702D382E302E30,android-dhcp-8.0.0,,,,0  N/A
DHCP_PATTERN = compile(
    INIT_REG +
    EVENT_REG +
    r'\s+\[MSWinEventLog.+?\]\s+.+\s+' +
    ALERT_REG +
    r'\s+(.*)\tN/A'  ## 11,12/08/24,20:01:29,Renew,192.168.0.0,xPhone.sth.local,38A4EDBA48B9,,640399471,0,,,,0x616E64726F69642D646863702D382E302E30,android-dhcp-8.0.0,,,,0  N/A
)
##
## 11,12/08/24,20:01:29,Renew,192.168.0.0,xPhone.sth.local,38A4EDBA48B9,,640399471,0,,,,0x616E64726F69642D646863702D382
DHCP_REST_PATTERN = compile(
    r'\s*(.+?),'
    r'(.+?),'
    r'(.+?),'
    r'(.*?),'
    r'(.*?),'
    r'(.*?),'
    r'(.*?),'
    r'(.*?),'
    r'(.*?),'
    r'(.*?),'
    r'(.*?),'
    r'(.*?),'
    r'(.*?),'
    r'(.*?),'
    r'(.*?),'
    r'(.*?),'
    r'(.*?),'
    r'(.*?),'
    r'(.*)'
)

## JUMP_1
##
## 2000-01-01 00:00:00 WindowsServer-1 (user/info) [MSWinEventLog     1       N/A     1089080 Sun] Dec 08 01:16:44 2024       N/A     N/A     N/A     N/A   N/A      N/A     N/A             [dns] 12/8/2024 1:16:44 AM 0B54 PACKET  000001D4F30A0C90 UDP Snd ***********   46a1 R Q [0001   D   NOERROR] AAAA   (9)speedtest(10)example(2)AaBbCc(2)id(0)  N/A
DNS_PATTERN = compile(
    INIT_REG +
    EVENT_REG +
    r'\s+\[MSWinEventLog.+?\]\s+.+\s+' +
    ALERT_REG +
    r'\s+(.*)\tN/A'  ## 12/8/2024 1:16:44 AM 0B54 PACKET  000001D4F30A0C90 UDP Snd ***********   46a1 R Q [0001   D   NOERROR] AAAA   (9)speedtest(10)example(2)AaBbCc(2)id(0)  N/A
)
##
## 12/8/2024 1:16:44 AM 0B54 PACKET  000001D4F30A0C90 UDP Snd ***********   46a1 R Q [0001   D   NOERROR] AAAA   (9)speedtest(10)example(2)AaBbCc(2)id(0)  N/A
DNS_REST_PATTERN = compile(
    r'\s*(.+?)'          ## 12/8/2024
    r'\s+(.+?)'          ## 1:16:44
    r'\s+(.+?)'          ## AM
    r'\s+(.+?)'          ## 0B54
    r'\s+(.+?)'          ## PACKET
    r'\s+(.+?)'          ## 000001D4F30A0C90
    r'\s+(.+?)'          ## UDP
    r'\s+(.+?)'          ## Snd
    r'\s+(.+?)'          ## ***********
    r'\s+(.+?)'          ## 46a1
    r'\s+(R?)'           ## R
    r'\s+([Q\?]?)'       ## Q
    r'\s+\[([^\s]+)'     ## 0001
    r'\s+([A-Za-z ]*?)'  ## D
    r'\s+([^\s]+)\]'     ## NOERROR
    r'\s+(.+?)'          ## AAAA
    r'\s+(.+)'           ## (9)speedtest(10)example(2)AaBbCc(2)id(0)
)

## 2000-01-01 00:00:00 Sensor-1 (local0/info) [filterlog] 85,,,1000006862,pppoe0,match,pass,out,4,0x0,,64,12345,0,DF,6,tcp,60,***********00,8.8.8.8,12345,443,40,S,12345,0,65535,,,,
## 2000-01-01 00:00:00 Sensor-1 (local0/info) [filterlog] 85,,,1000006862,pppoe0,match,pass,out,6,0x0,0,64,udp,17,80,2001:db8::1,2001:db8::2,12345,53,40
FILTERLOG_PATTERN = compile(INIT_REG + EVENT_REG + ALERT_REG + r'\s+(.*)')

## 2000-01-01 00:00:00 Router-1 (local7/err) [%LINK-3-UPDOWN] Interface Foreign Exchange Office 0/0/0, changed state to Administrative Shutdown
ROUTER_PATTERN = compile(INIT_REG + EVENT_REG + ALERT_REG + r'\s+(.*)')

## 2000-01-01 00:00:00 RouterBoard-1 (local7/info) [respond] established from *******, port: 37 to 5.6.7.8
ROUTERBOARD_PATTERN = compile(INIT_REG + EVENT_REG + ALERT_REG + r'\s+(.*)')

## 2000-01-01 00:00:00 Sensor-1 (auth/alert) [snort] [1:1448:20] POLICY-OTHER Microsoft [Classification: Generic Command] [Priority: 3] {TCP} *******:94 -> 5.6.7.8:32
SNORT_PATTERN = compile(
    INIT_REG +
    EVENT_REG +
    ALERT_REG +
    r'\s+\[(\d+:\d+:\d+)\]'          ## [1:1448:20]
    r'\s+(.+?)'                      ## POLICY-OTHER Microsoft
    r'\s+\[Classification: (.+?)\]'  ## Generic Command
    r'\s+\[Priority: (\d+)\]'        ## 3
    r'\s+\{(\w+)\}'                  ## TCP
    r'\s+([\d\.]+)(?::(\d+))?'       ## *******:94
    r'\s+->'
    r'\s+([\d\.]+)(?::(\d+))?'       ## 5.6.7.8:32
)

## 2000-01-01 00:00:00 Sensor-1 (local4/info) [(squid-1)] 1729759769.387 0 *********** NONE_NONE/503 4101 GET https://example.org/AaBbCc - HIER_NONE/- text/html
SQUID_PATTERN = compile(
    INIT_REG +
    EVENT_REG +
    ALERT_REG +
    r'\s+(.+?)'  ## 1729759769.387
    r'\s+(.+?)'  ## 0
    r'\s+(.+?)'  ## ***********
    r'\s+(.+?)'  ## NONE_NONE
    r'/(.+?)'    ## 503
    r'\s+(.+?)'  ## 4101
    r'\s+(.+?)'  ## GET
    r'\s+(.+?)'  ## https://example.org/AaBbCc
    r'\s+(.+?)'  ## -
    r'\s+(.+?)'  ## HIER_NONE
    r'/(.+?)'    ## -
    r'\s+(.+)'   ## text/html
)

## 2000-01-01 00:00:00 Switch-1 (local7/err) [%LINK-3-UPDOWN] Te2/1/1: Rx power low warning; Operating value: -14.8 dBm, Threshold value: -14.1 dBm. (Dis-Sw2-2)
SWITCH_PATTERN = compile(INIT_REG + EVENT_REG + ALERT_REG + r'\s+(.*)')

## 2000-01-01 00:00:00 Sensor-1 (daemon/err) [php-fpm] /index.php: Successful login for user 'admin' from: ******* (Local Database)
## 2000-01-01 00:00:00 Sensor-1 (daemon/err) [php-fpm] /index.php: User logged out for user 'admin' from: *********** (Local Database)
USERAUDIT_PATTERN = compile(INIT_REG + EVENT_REG + ALERT_REG + r'\s+(.*)')

## 2000-01-01 00:00:00 Sensor-1 (user/notice) [openvpn] openvpn server 'someserver' user 'someuser' address '*******:80' - connecting
USERNOTICE_PATTERN = compile(
    INIT_REG +
    EVENT_REG +
    ALERT_REG +
    r"\s+openvpn\s+server\s+'(.+?)'"   ## someserver
    r"\s+user\s+'(.+?)'"               ## someuser
    r"\s+address\s+'([\d\.]+):(\d+)'"  ## *******  80
    r"\s+.+?"
    r"\s+(.+)"                         ## connecting
)

## 2000-01-01 00:00:00 Sensor-1 (user/warning) [dpinger] GRETUN_TUNNELV4 ***********: Alarm latency 0us stddev 0us loss 100%
USERWARNING_PATTERN = compile(INIT_REG + EVENT_REG + ALERT_REG + r'\s+(.*)')

## 2000-01-01 00:00:00 VMware-1 (local0/info) [dnsmasq] 2024-06-06T04:46:52.117+04:30 INFO sth-mgmt[18989] [SthProfiler::logProfile opID=SWI-1023qwer-345 MetroConfig: 0.00s
VMWARE_PATTERN = compile(INIT_REG + EVENT_REG + ALERT_REG + r'\s+(.*)')

## 2000-01-01 00:00:00 VPNSERVER (user/info) [MSWinEventLog	1	System	5061875	Mon] Jun 30 20:28:56 2025       20272   RemoteAccess    N/A     N/A     Information     VPNSERVER.sth.local     N/A         RoutingDomainID- {********-0000-0000-0000-********0000}: CoID={NA}: The user MYDOMAIN\n.peterson connected on port VPN1-123 on 6/30/2025 at 5:36 PM and disconnected on 6/30/2025 at 8:28 PM.  The user was active for 172 minutes 12 seconds.  ******** bytes were sent and 7613881 bytes were received. The reason for disconnecting was user request. The tunnel used was WAN Miniport (PPTP). The quarantine state was .      654321
VPNSERVER_PATTERN = compile(
    INIT_REG +
    EVENT_REG +
    ALERT_REG +
    r'.*?The user\s+([^\\]+)'                  ## MYDOMAIN
    r'\\(.+)\s+'                               ## n.peterson
                                               ##   NOTE: may contain spaces too,
                                               ##   e.g. a.william jackson
    r'connected on port\s+(\S+)\s+.*?'         ## VPN1-123
    r'The user was active for\s+([\w\s]+?)\.'  ## 172 minutes 12 seconds
    r'\s+(\d+)\s+bytes were sent and'          ## ********
    r'\s+(\d+)\s+bytes were received'          ## 7613881
)

## 2000-01-01 00:00:00 WindowsServer-1 (user/info) [MSWinEventLog    1    Security    4428046    Tue] Dec 07 00:00:22 2024	4634	Microsoft-Windows-Security-Auditing	N/A	N/A	Success Audit	sth.sth.local	Logoff		An account was logged off.    Subject:   Security ID:  S-1-5-18   Account Name:  FooName$   Account Domain:  BarDomain   Logon ID:  0x2B3F21    Logon Type:   3    This event is generated when a logon session is destroyed. It may be positively correlated with a logon event using the Logon ID value. Logon IDs are only unique between reboots on the same computer.	********
WINDOWSSERVER_PATTERN = compile(
    INIT_REG +
    EVENT_REG +
    ALERT_REG +
    r'\s+(.*)'
)
##
## ... Account Name:  FooName$   Account Domain:  BarDomain   Logon ID: ...
WS_AN_AD_PATTERN = compile(
    r'.+Account Name:\s+(.+?)'     ## FooName$
    r'\s+Account Domain:\s+(.+?)'  ## BarDomain
    r'\s+Logon ID:.+'
)
##
## ... Source Workstation: OMM-STH  Error Code: ...
WS_SW_PATTERN = compile(
    r'.+Source Workstation:\s+([^\s]+?)'  ## OMM-STH
    r'\s+Error Code:.+'
)
